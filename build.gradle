plugins {
    id 'com.github.johnrengelman.shadow' version '7.1.0'
    id 'java'
}

shadowJar {
    mergeServiceFiles()
    manifest {
        attributes 'Main-Class': 'jobs.Main'
    }
    // Enable the zip64 extension
    zip64 true
}

group 'vtp.com.vn'
version '1.0.0'

repositories {
    jcenter()
    mavenLocal()
    mavenCentral()
    maven {
        url "https://oss.sonatype.org/content/repositories/snapshots"
    }
    sourceCompatibility = 1.8
    targetCompatibility = 1.8
}

dependencies {
    implementation group: 'commons-io', name: 'commons-io', version: '2.5'
    implementation group: 'com.typesafe', name: 'config', version: '1.0.2'

    implementation ("org.apache.phoenix:phoenix-core:5.1.3"){
        exclude group: 'org.apache.omid'
    }
    implementation group: 'org.yaml', name: 'snakeyaml', version: '1.29'
    compileOnly group: 'org.projectlombok', name: 'lombok', version: '0.11.0'
    implementation 'org.apache.phoenix:phoenix-hbase-compat-2.4.1:5.1.3'
    implementation group: 'log4j', name: 'log4j', version: '1.2.17'
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.2.22'
    implementation group: 'com.google.guava', name: 'guava', version: '11.0.2'
    implementation group: 'org.apache.poi', name: 'poi', version: '3.9'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}
