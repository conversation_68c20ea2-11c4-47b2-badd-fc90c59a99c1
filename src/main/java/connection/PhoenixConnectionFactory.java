package connection;


import configs.Configuration;

import java.sql.Connection;
import java.sql.DriverManager;

import static configs.ConfigurationFactory.getConfigInstance;

public class PhoenixConnectionFactory {

    private PhoenixConnectionFactory() {}
    private static PhoenixConnectionFactory ourInstance;
    private static Configuration config;

    public static synchronized PhoenixConnectionFactory getInstance() {
        if (ourInstance == null) {
            ourInstance = new PhoenixConnectionFactory();
            config =  getConfigInstance();
        }
        return ourInstance;
    }

    public Connection getPhoenixConnection() throws Exception {
        String conn = config.getConfig("PHOENIX.URL");
        return DriverManager.getConnection(conn);
    }
}
