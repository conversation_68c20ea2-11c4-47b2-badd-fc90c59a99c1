package model.doanhthusanluong;

import java.io.Serializable;

public class DoanhThuSanLuong implements Serializable {
    private String ngayBaoCao;
    private String maChiNhanh;
    private String maBuuCuc;
    private Double doanhThuThang;
    private Double doanhThuCPTN;
    private Double doanhThuVT;
    private Double doanhThuEXP;
    private Double doanhThuKho;
    private Long sanLuongThang;
    private Long sanLuongCPTN;
    private Long sanLuongVT;
    private Long sanLuongEXP;
    private Long sanLuongKho;
    private Long updateTime;

    public DoanhThuSanLuong() {
    }

    public DoanhThuSanLuong(String ngayBaoCao, String maChiNhanh, String maBuuCuc, Double doanhThuThang, Double doanhThuCPTN, Double doanhThuVT, Double doanhThuEXP, Double doanhThuKho, Long sanLuongThang, Long sanLuongCPTN, Long sanLuongVT, Long sanLuongEXP, Long sanLuongKho, Long updateTime) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.doanhThuThang = doanhThuThang;
        this.doanhThuCPTN = doanhThuCPTN;
        this.doanhThuVT = doanhThuVT;
        this.doanhThuEXP = doanhThuEXP;
        this.doanhThuKho = doanhThuKho;
        this.sanLuongThang = sanLuongThang;
        this.sanLuongCPTN = sanLuongCPTN;
        this.sanLuongVT = sanLuongVT;
        this.sanLuongEXP = sanLuongEXP;
        this.sanLuongKho = sanLuongKho;
        this.updateTime = updateTime;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String month) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public Double getDoanhThuThang() {
        return doanhThuThang;
    }

    public void setDoanhThuThang(Double doanhThuThang) {
        this.doanhThuThang = doanhThuThang;
    }

    public Double getDoanhThuCPTN() {
        return doanhThuCPTN;
    }

    public void setDoanhThuCPTN(Double doanhThuCPTN) {
        this.doanhThuCPTN = doanhThuCPTN;
    }

    public Double getDoanhThuVT() {
        return doanhThuVT;
    }

    public void setDoanhThuVT(Double doanhThuVT) {
        this.doanhThuVT = doanhThuVT;
    }

    public Double getDoanhThuEXP() {
        return doanhThuEXP;
    }

    public void setDoanhThuEXP(Double doanhThuEXP) {
        this.doanhThuEXP = doanhThuEXP;
    }

    public Double getDoanhThuKho() {
        return doanhThuKho;
    }

    public void setDoanhThuKho(Double doanhThuKho) {
        this.doanhThuKho = doanhThuKho;
    }

    public Long getSanLuongThang() {
        return sanLuongThang;
    }

    public void setSanLuongThang(Long sanLuongThang) {
        this.sanLuongThang = sanLuongThang;
    }

    public Long getSanLuongCPTN() {
        return sanLuongCPTN;
    }

    public void setSanLuongCPTN(Long sanLuongCPTN) {
        this.sanLuongCPTN = sanLuongCPTN;
    }

    public Long getSanLuongVT() {
        return sanLuongVT;
    }

    public void setSanLuongVT(Long sanLuongVT) {
        this.sanLuongVT = sanLuongVT;
    }

    public Long getSanLuongEXP() {
        return sanLuongEXP;
    }

    public void setSanLuongEXP(Long sanLuongEXP) {
        this.sanLuongEXP = sanLuongEXP;
    }

    public Long getSanLuongKho() {
        return sanLuongKho;
    }

    public void setSanLuongKho(Long sanLuongKho) {
        this.sanLuongKho = sanLuongKho;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
