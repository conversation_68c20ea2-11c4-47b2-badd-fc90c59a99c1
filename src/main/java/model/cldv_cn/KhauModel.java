package model.cldv_cn;

public class KhauModel {
    private String ngayBaoCao;
    private String maChiNhanh;
    private String maBuuCuc;
    private String loaiChiTieu;

    // khau phat
    private Long slPhatYC;
    private Long slPhatTCThucTe;
    private Long slPhatDungGio;
    private Long slPhatTC501;
    private Long slHoan;
    private float tlPhatDG;

    private float tlPhatTC;
    private float tlHoan;

    // khau thu
    private Long slThuYC;
    private Long slThuTC;
    private Long slThuTCDungGio;
    private float tlThuDG;

    private float tlThuTC;

    private int luyKe;

    public KhauModel() {
    }

    public Long getSlThuYC() {
        return slThuYC;
    }

    public void setSlThuYC(Long slThuYC) {
        this.slThuYC = slThuYC;
    }

    public Long getSlThuTC() {
        return slThuTC;
    }

    public void setSlThuTC(Long slThuTC) {
        this.slThuTC = slThuTC;
    }

    public Long getSlThuTCDungGio() {
        return slThuTCDungGio;
    }

    public void setSlThuTCDungGio(Long slThuTCDungGio) {
        this.slThuTCDungGio = slThuTCDungGio;
    }

    public float getTlThuDG() {
        return tlThuDG;
    }

    public void setTlThuDG(float tlThuDG) {
        this.tlThuDG = tlThuDG;
    }

    public float getTlThuTC() {
        return tlThuTC;
    }

    public void setTlThuTC(float tlThuTC) {
        this.tlThuTC = tlThuTC;
    }

    public float getTlHoan() {
        return tlHoan;
    }

    public void setTlHoan(float tlHoan) {
        this.tlHoan = tlHoan;
    }

    public Long getSlHoan() {
        return slHoan;
    }

    public void setSlHoan(Long slHoan) {
        this.slHoan = slHoan;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public String getLoaiChiTieu() {
        return loaiChiTieu;
    }

    public void setLoaiChiTieu(String loaiChiTieu) {
        this.loaiChiTieu = loaiChiTieu;
    }

    public Long getSlPhatYC() {
        return slPhatYC;
    }

    public void setSlPhatYC(Long slPhatYC) {
        this.slPhatYC = slPhatYC;
    }

    public Long getSlPhatTCThucTe() {
        return slPhatTCThucTe;
    }

    public void setSlPhatTCThucTe(Long slPhatTCThucTe) {
        this.slPhatTCThucTe = slPhatTCThucTe;
    }

    public Long getSlPhatDungGio() {
        return slPhatDungGio;
    }

    public void setSlPhatDungGio(Long slPhatDungGio) {
        this.slPhatDungGio = slPhatDungGio;
    }

    public Long getSlPhatTC501() {
        return slPhatTC501;
    }

    public void setSlPhatTC501(Long slPhatTC501) {
        this.slPhatTC501 = slPhatTC501;
    }

    public float getTlPhatDG() {
        return tlPhatDG;
    }

    public void setTlPhatDG(float tlPhatDG) {
        this.tlPhatDG = tlPhatDG;
    }

    public float getTlPhatTC() {
        return tlPhatTC;
    }

    public void setTlPhatTC(float tlPhatTC) {
        this.tlPhatTC = tlPhatTC;
    }

    public int getLuyKe() {
        return luyKe;
    }

    public void setLuyKe(int luyKe) {
        this.luyKe = luyKe;
    }
}
