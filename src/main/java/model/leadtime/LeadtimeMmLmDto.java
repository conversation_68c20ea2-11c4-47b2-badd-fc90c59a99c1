package model.leadtime;

import lombok.*;

import java.io.Serializable;

public class LeadtimeMmLmDto implements Serializable {
    private String ngayBaoCao;
    private String maChiNhanh;
    private String maBuuCuc;
    /////////////////////////
    private Float tgTtAll;
    private Float tgMmAll;
    private Float tgLmAll;
    private Float tgNoiTinhAll;
    private Float tgNoiMienAll;
    private Float tgLienMienAll;
    /////////////////////////
    private Float tgTtNhanh;
    private Float tgMmNhanh;
    private Float tgLmNhanh;
    private Float tgNoiTinhNhanh;
    private Float tgNoiMienNhanh;
    private Float tgLienMienNhanh;
    /////////////////////////
    private Float tgTtTk;
    private Float tgMmTk;
    private Float tgLmTk;
    private Float tgNoiTinhTk;
    private Float tgNoiMienTk;
    private Float tgLienMienTk;
    /////////////////////////
    private Float tgTtKienTk;
    private Float tgMmKienTk;
    private Float tgLmKienTk;
    private Float tgNoiTinhKienTk;
    private Float tgNoiMienKienTk;
    private Float tgLienMienKienTk;
    /////////////////////////
    private Float tgTtTachKienTk;
    private Float tgMmTachKienTk;
    private Float tgLmTachKienTk;
    private Float tgNoiTinhTachKienTk;
    private Float tgNoiMienTachKienTk;
    private Float tgLienMienTachKienTk;
    // cột số lượng
    private long slDonAll;
    private long slNoiTinhAll;
    private long slNoiMienAll;
    private long slLienMienAll;
    private long slTtNhanh;
    private long slMmNhanh;
    private long slLmNhanh;
    private long slNoiTinhNhanh;
    private long slNoiMienNhanh;
    private long slLienMienNhanh;
    private long slTtTk;
    private long slMmTk;
    private long slLmTk;
    private long slNoiTinhTk;
    private long slNoiMienTk;
    private long slLienMienTk;
    private long slTtKienTk;
    private long slMmKienTk;
    private long slLmKienTk;
    private long slNoiTinhKienTk;
    private long slNoiMienKienTk;
    private long slLienMienKienTk;
    private long slTtTachKienTk;
    private long slMmTachKienTk;
    private long slLmTachKienTk;
    private long slNoiTinhTachKienTk;
    private long slNoiMienTachKienTk;
    private long slLienMienTachKienTk;

    private long slFmAll;
    private long slFmNhanh;
    private long slFmTk;
    private long slFmKienTk;
    private long slFmTachKienTk;

    private float tgMmNoiMien;
    private float tgMmLienMien;

    public float getTgMmNoiMien() {
        return tgMmNoiMien;
    }

    public void setTgMmNoiMien(float tgMmNoiMien) {
        this.tgMmNoiMien = tgMmNoiMien;
    }

    public float getTgMmLienMien() {
        return tgMmLienMien;
    }

    public void setTgMmLienMien(float tgMmLienMien) {
        this.tgMmLienMien = tgMmLienMien;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public Float getTgTtAll() {
        return tgTtAll;
    }

    public void setTgTtAll(Float tgTtAll) {
        this.tgTtAll = tgTtAll;
    }

    public Float getTgMmAll() {
        return tgMmAll;
    }

    public void setTgMmAll(Float tgMmAll) {
        this.tgMmAll = tgMmAll;
    }

    public Float getTgLmAll() {
        return tgLmAll;
    }

    public void setTgLmAll(Float tgLmAll) {
        this.tgLmAll = tgLmAll;
    }

    public Float getTgNoiTinhAll() {
        return tgNoiTinhAll;
    }

    public void setTgNoiTinhAll(Float tgNoiTinhAll) {
        this.tgNoiTinhAll = tgNoiTinhAll;
    }

    public Float getTgNoiMienAll() {
        return tgNoiMienAll;
    }

    public void setTgNoiMienAll(Float tgNoiMienAll) {
        this.tgNoiMienAll = tgNoiMienAll;
    }

    public Float getTgLienMienAll() {
        return tgLienMienAll;
    }

    public void setTgLienMienAll(Float tgLienMienAll) {
        this.tgLienMienAll = tgLienMienAll;
    }

    public Float getTgTtNhanh() {
        return tgTtNhanh;
    }

    public void setTgTtNhanh(Float tgTtNhanh) {
        this.tgTtNhanh = tgTtNhanh;
    }

    public Float getTgMmNhanh() {
        return tgMmNhanh;
    }

    public void setTgMmNhanh(Float tgMmNhanh) {
        this.tgMmNhanh = tgMmNhanh;
    }

    public Float getTgLmNhanh() {
        return tgLmNhanh;
    }

    public void setTgLmNhanh(Float tgLmNhanh) {
        this.tgLmNhanh = tgLmNhanh;
    }

    public Float getTgNoiTinhNhanh() {
        return tgNoiTinhNhanh;
    }

    public void setTgNoiTinhNhanh(Float tgNoiTinhNhanh) {
        this.tgNoiTinhNhanh = tgNoiTinhNhanh;
    }

    public Float getTgNoiMienNhanh() {
        return tgNoiMienNhanh;
    }

    public void setTgNoiMienNhanh(Float tgNoiMienNhanh) {
        this.tgNoiMienNhanh = tgNoiMienNhanh;
    }

    public Float getTgLienMienNhanh() {
        return tgLienMienNhanh;
    }

    public void setTgLienMienNhanh(Float tgLienMienNhanh) {
        this.tgLienMienNhanh = tgLienMienNhanh;
    }

    public Float getTgTtTk() {
        return tgTtTk;
    }

    public void setTgTtTk(Float tgTtTk) {
        this.tgTtTk = tgTtTk;
    }

    public Float getTgMmTk() {
        return tgMmTk;
    }

    public void setTgMmTk(Float tgMmTk) {
        this.tgMmTk = tgMmTk;
    }

    public Float getTgLmTk() {
        return tgLmTk;
    }

    public void setTgLmTk(Float tgLmTk) {
        this.tgLmTk = tgLmTk;
    }

    public Float getTgNoiTinhTk() {
        return tgNoiTinhTk;
    }

    public void setTgNoiTinhTk(Float tgNoiTinhTk) {
        this.tgNoiTinhTk = tgNoiTinhTk;
    }

    public Float getTgNoiMienTk() {
        return tgNoiMienTk;
    }

    public void setTgNoiMienTk(Float tgNoiMienTk) {
        this.tgNoiMienTk = tgNoiMienTk;
    }

    public Float getTgLienMienTk() {
        return tgLienMienTk;
    }

    public void setTgLienMienTk(Float tgLienMienTk) {
        this.tgLienMienTk = tgLienMienTk;
    }

    public Float getTgTtKienTk() {
        return tgTtKienTk;
    }

    public void setTgTtKienTk(Float tgTtKienTk) {
        this.tgTtKienTk = tgTtKienTk;
    }

    public Float getTgMmKienTk() {
        return tgMmKienTk;
    }

    public void setTgMmKienTk(Float tgMmKienTk) {
        this.tgMmKienTk = tgMmKienTk;
    }

    public Float getTgLmKienTk() {
        return tgLmKienTk;
    }

    public void setTgLmKienTk(Float tgLmKienTk) {
        this.tgLmKienTk = tgLmKienTk;
    }

    public Float getTgNoiTinhKienTk() {
        return tgNoiTinhKienTk;
    }

    public void setTgNoiTinhKienTk(Float tgNoiTinhKienTk) {
        this.tgNoiTinhKienTk = tgNoiTinhKienTk;
    }

    public Float getTgNoiMienKienTk() {
        return tgNoiMienKienTk;
    }

    public void setTgNoiMienKienTk(Float tgNoiMienKienTk) {
        this.tgNoiMienKienTk = tgNoiMienKienTk;
    }

    public Float getTgLienMienKienTk() {
        return tgLienMienKienTk;
    }

    public void setTgLienMienKienTk(Float tgLienMienKienTk) {
        this.tgLienMienKienTk = tgLienMienKienTk;
    }

    public Float getTgTtTachKienTk() {
        return tgTtTachKienTk;
    }

    public void setTgTtTachKienTk(Float tgTtTachKienTk) {
        this.tgTtTachKienTk = tgTtTachKienTk;
    }

    public Float getTgMmTachKienTk() {
        return tgMmTachKienTk;
    }

    public void setTgMmTachKienTk(Float tgMmTachKienTk) {
        this.tgMmTachKienTk = tgMmTachKienTk;
    }

    public Float getTgLmTachKienTk() {
        return tgLmTachKienTk;
    }

    public void setTgLmTachKienTk(Float tgLmTachKienTk) {
        this.tgLmTachKienTk = tgLmTachKienTk;
    }

    public Float getTgNoiTinhTachKienTk() {
        return tgNoiTinhTachKienTk;
    }

    public void setTgNoiTinhTachKienTk(Float tgNoiTinhTachKienTk) {
        this.tgNoiTinhTachKienTk = tgNoiTinhTachKienTk;
    }

    public Float getTgNoiMienTachKienTk() {
        return tgNoiMienTachKienTk;
    }

    public void setTgNoiMienTachKienTk(Float tgNoiMienTachKienTk) {
        this.tgNoiMienTachKienTk = tgNoiMienTachKienTk;
    }

    public Float getTgLienMienTachKienTk() {
        return tgLienMienTachKienTk;
    }

    public void setTgLienMienTachKienTk(Float tgLienMienTachKienTk) {
        this.tgLienMienTachKienTk = tgLienMienTachKienTk;
    }

    public long getSlDonAll() {
        return slDonAll;
    }

    public void setSlDonAll(long slDonAll) {
        this.slDonAll = slDonAll;
    }

    public long getSlNoiTinhAll() {
        return slNoiTinhAll;
    }

    public void setSlNoiTinhAll(long slNoiTinhAll) {
        this.slNoiTinhAll = slNoiTinhAll;
    }

    public long getSlNoiMienAll() {
        return slNoiMienAll;
    }

    public void setSlNoiMienAll(long slNoiMienAll) {
        this.slNoiMienAll = slNoiMienAll;
    }

    public long getSlLienMienAll() {
        return slLienMienAll;
    }

    public void setSlLienMienAll(long slLienMienAll) {
        this.slLienMienAll = slLienMienAll;
    }

    public long getSlTtNhanh() {
        return slTtNhanh;
    }

    public void setSlTtNhanh(long slTtNhanh) {
        this.slTtNhanh = slTtNhanh;
    }

    public long getSlMmNhanh() {
        return slMmNhanh;
    }

    public void setSlMmNhanh(long slMmNhanh) {
        this.slMmNhanh = slMmNhanh;
    }

    public long getSlLmNhanh() {
        return slLmNhanh;
    }

    public void setSlLmNhanh(long slLmNhanh) {
        this.slLmNhanh = slLmNhanh;
    }

    public long getSlNoiTinhNhanh() {
        return slNoiTinhNhanh;
    }

    public void setSlNoiTinhNhanh(long slNoiTinhNhanh) {
        this.slNoiTinhNhanh = slNoiTinhNhanh;
    }

    public long getSlNoiMienNhanh() {
        return slNoiMienNhanh;
    }

    public void setSlNoiMienNhanh(long slNoiMienNhanh) {
        this.slNoiMienNhanh = slNoiMienNhanh;
    }

    public long getSlLienMienNhanh() {
        return slLienMienNhanh;
    }

    public void setSlLienMienNhanh(long slLienMienNhanh) {
        this.slLienMienNhanh = slLienMienNhanh;
    }

    public long getSlTtTk() {
        return slTtTk;
    }

    public void setSlTtTk(long slTtTk) {
        this.slTtTk = slTtTk;
    }

    public long getSlMmTk() {
        return slMmTk;
    }

    public void setSlMmTk(long slMmTk) {
        this.slMmTk = slMmTk;
    }

    public long getSlLmTk() {
        return slLmTk;
    }

    public void setSlLmTk(long slLmTk) {
        this.slLmTk = slLmTk;
    }

    public long getSlNoiTinhTk() {
        return slNoiTinhTk;
    }

    public void setSlNoiTinhTk(long slNoiTinhTk) {
        this.slNoiTinhTk = slNoiTinhTk;
    }

    public long getSlNoiMienTk() {
        return slNoiMienTk;
    }

    public void setSlNoiMienTk(long slNoiMienTk) {
        this.slNoiMienTk = slNoiMienTk;
    }

    public long getSlLienMienTk() {
        return slLienMienTk;
    }

    public void setSlLienMienTk(long slLienMienTk) {
        this.slLienMienTk = slLienMienTk;
    }

    public long getSlTtKienTk() {
        return slTtKienTk;
    }

    public void setSlTtKienTk(long slTtKienTk) {
        this.slTtKienTk = slTtKienTk;
    }

    public long getSlMmKienTk() {
        return slMmKienTk;
    }

    public void setSlMmKienTk(long slMmKienTk) {
        this.slMmKienTk = slMmKienTk;
    }

    public long getSlLmKienTk() {
        return slLmKienTk;
    }

    public void setSlLmKienTk(long slLmKienTk) {
        this.slLmKienTk = slLmKienTk;
    }

    public long getSlNoiTinhKienTk() {
        return slNoiTinhKienTk;
    }

    public void setSlNoiTinhKienTk(long slNoiTinhKienTk) {
        this.slNoiTinhKienTk = slNoiTinhKienTk;
    }

    public long getSlNoiMienKienTk() {
        return slNoiMienKienTk;
    }

    public void setSlNoiMienKienTk(long slNoiMienKienTk) {
        this.slNoiMienKienTk = slNoiMienKienTk;
    }

    public long getSlLienMienKienTk() {
        return slLienMienKienTk;
    }

    public void setSlLienMienKienTk(long slLienMienKienTk) {
        this.slLienMienKienTk = slLienMienKienTk;
    }

    public long getSlTtTachKienTk() {
        return slTtTachKienTk;
    }

    public void setSlTtTachKienTk(long slTtTachKienTk) {
        this.slTtTachKienTk = slTtTachKienTk;
    }

    public long getSlMmTachKienTk() {
        return slMmTachKienTk;
    }

    public void setSlMmTachKienTk(long slMmTachKienTk) {
        this.slMmTachKienTk = slMmTachKienTk;
    }

    public long getSlLmTachKienTk() {
        return slLmTachKienTk;
    }

    public void setSlLmTachKienTk(long slLmTachKienTk) {
        this.slLmTachKienTk = slLmTachKienTk;
    }

    public long getSlNoiTinhTachKienTk() {
        return slNoiTinhTachKienTk;
    }

    public void setSlNoiTinhTachKienTk(long slNoiTinhTachKienTk) {
        this.slNoiTinhTachKienTk = slNoiTinhTachKienTk;
    }

    public long getSlNoiMienTachKienTk() {
        return slNoiMienTachKienTk;
    }

    public void setSlNoiMienTachKienTk(long slNoiMienTachKienTk) {
        this.slNoiMienTachKienTk = slNoiMienTachKienTk;
    }

    public long getSlLienMienTachKienTk() {
        return slLienMienTachKienTk;
    }

    public void setSlLienMienTachKienTk(long slLienMienTachKienTk) {
        this.slLienMienTachKienTk = slLienMienTachKienTk;
    }

    public long getSlFmAll() {
        return slFmAll;
    }

    public void setSlFmAll(long slFmAll) {
        this.slFmAll = slFmAll;
    }

    public long getSlFmNhanh() {
        return slFmNhanh;
    }

    public void setSlFmNhanh(long slFmNhanh) {
        this.slFmNhanh = slFmNhanh;
    }

    public long getSlFmTk() {
        return slFmTk;
    }

    public void setSlFmTk(long slFmTk) {
        this.slFmTk = slFmTk;
    }

    public long getSlFmKienTk() {
        return slFmKienTk;
    }

    public void setSlFmKienTk(long slFmKienTk) {
        this.slFmKienTk = slFmKienTk;
    }

    public long getSlFmTachKienTk() {
        return slFmTachKienTk;
    }

    public void setSlFmTachKienTk(long slFmTachKienTk) {
        this.slFmTachKienTk = slFmTachKienTk;
    }
}
