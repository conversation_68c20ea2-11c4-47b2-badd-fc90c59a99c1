package model.leadtime;

public class LeadTimeLkThang {
    private String ngayBaoCao; //key
    private String maChiNhanh; // key
    private String maBuuCuc;// key
    private double tgTtAll;
    private double tgFmAll;
    private double tgMmAll;
    private double tgLmAll;
    private double tgNoiTinhAll;
    private double tgNoiMienAll;
    private double tgLienMienAll;
    private double tgTtNhanh;
    private double tgFmNhanh;
    private double tgMmNhanh;
    private double tgLmNhanh;
    private double tgNoiTinhNhanh;
    private double tgNoiMienNhanh;
    private double tgLienMienNhanh;
    private double tgTtTk;
    private double tgFmTk;
    private double tgMmTk;
    private double tgLmTk;
    private double tgNoiTinhTk;
    private double tgNoiMienTk;
    private double tgLienMienTk;
    private double tgTtKienTk;
    private double tgFmKienTk;
    private double tgMmKienTk;
    private double tgLmKienTk;
    private double tgNoiTinhKienTk;
    private double tgNoiMienKienTk;
    private double tgLienMienKienTk;
    private double tgTtTachKienTk;
    private double tgFmTachKienTk;
    private double tgMmTachKienTk;
    private double tgLmTachKienTk;
    private double tgNoiTinhTachKienTk;
    private double tgNoiMienTachKienTk;
    private double tgLienMienTachKienTk;

    private double tgMmNoiMien;
    private double tgMmLienMien;

    public LeadTimeLkThang() {
    }

    public double getTgMmNoiMien() {
        return tgMmNoiMien;
    }

    public void setTgMmNoiMien(double tgMmNoiMien) {
        this.tgMmNoiMien = tgMmNoiMien;
    }

    public double getTgMmLienMien() {
        return tgMmLienMien;
    }

    public void setTgMmLienMien(double tgMmLienMien) {
        this.tgMmLienMien = tgMmLienMien;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public double getTgTtAll() {
        return tgTtAll;
    }

    public void setTgTtAll(double tgTtAll) {
        this.tgTtAll = tgTtAll;
    }

    public double getTgFmAll() {
        return tgFmAll;
    }

    public void setTgFmAll(double tgFmAll) {
        this.tgFmAll = tgFmAll;
    }

    public double getTgMmAll() {
        return tgMmAll;
    }

    public void setTgMmAll(double tgMmAll) {
        this.tgMmAll = tgMmAll;
    }

    public double getTgLmAll() {
        return tgLmAll;
    }

    public void setTgLmAll(double tgLmAll) {
        this.tgLmAll = tgLmAll;
    }

    public double getTgNoiTinhAll() {
        return tgNoiTinhAll;
    }

    public void setTgNoiTinhAll(double tgNoiTinhAll) {
        this.tgNoiTinhAll = tgNoiTinhAll;
    }

    public double getTgNoiMienAll() {
        return tgNoiMienAll;
    }

    public void setTgNoiMienAll(double tgNoiMienAll) {
        this.tgNoiMienAll = tgNoiMienAll;
    }

    public double getTgLienMienAll() {
        return tgLienMienAll;
    }

    public void setTgLienMienAll(double tgLienMienAll) {
        this.tgLienMienAll = tgLienMienAll;
    }

    public double getTgTtNhanh() {
        return tgTtNhanh;
    }

    public void setTgTtNhanh(double tgTtNhanh) {
        this.tgTtNhanh = tgTtNhanh;
    }

    public double getTgFmNhanh() {
        return tgFmNhanh;
    }

    public void setTgFmNhanh(double tgFmNhanh) {
        this.tgFmNhanh = tgFmNhanh;
    }

    public double getTgMmNhanh() {
        return tgMmNhanh;
    }

    public void setTgMmNhanh(double tgMmNhanh) {
        this.tgMmNhanh = tgMmNhanh;
    }

    public double getTgLmNhanh() {
        return tgLmNhanh;
    }

    public void setTgLmNhanh(double tgLmNhanh) {
        this.tgLmNhanh = tgLmNhanh;
    }

    public double getTgNoiTinhNhanh() {
        return tgNoiTinhNhanh;
    }

    public void setTgNoiTinhNhanh(double tgNoiTinhNhanh) {
        this.tgNoiTinhNhanh = tgNoiTinhNhanh;
    }

    public double getTgNoiMienNhanh() {
        return tgNoiMienNhanh;
    }

    public void setTgNoiMienNhanh(double tgNoiMienNhanh) {
        this.tgNoiMienNhanh = tgNoiMienNhanh;
    }

    public double getTgLienMienNhanh() {
        return tgLienMienNhanh;
    }

    public void setTgLienMienNhanh(double tgLienMienNhanh) {
        this.tgLienMienNhanh = tgLienMienNhanh;
    }

    public double getTgTtTk() {
        return tgTtTk;
    }

    public void setTgTtTk(double tgTtTk) {
        this.tgTtTk = tgTtTk;
    }

    public double getTgFmTk() {
        return tgFmTk;
    }

    public void setTgFmTk(double tgFmTk) {
        this.tgFmTk = tgFmTk;
    }

    public double getTgMmTk() {
        return tgMmTk;
    }

    public void setTgMmTk(double tgMmTk) {
        this.tgMmTk = tgMmTk;
    }

    public double getTgLmTk() {
        return tgLmTk;
    }

    public void setTgLmTk(double tgLmTk) {
        this.tgLmTk = tgLmTk;
    }

    public double getTgNoiTinhTk() {
        return tgNoiTinhTk;
    }

    public void setTgNoiTinhTk(double tgNoiTinhTk) {
        this.tgNoiTinhTk = tgNoiTinhTk;
    }

    public double getTgNoiMienTk() {
        return tgNoiMienTk;
    }

    public void setTgNoiMienTk(double tgNoiMienTk) {
        this.tgNoiMienTk = tgNoiMienTk;
    }

    public double getTgLienMienTk() {
        return tgLienMienTk;
    }

    public void setTgLienMienTk(double tgLienMienTk) {
        this.tgLienMienTk = tgLienMienTk;
    }

    public double getTgTtKienTk() {
        return tgTtKienTk;
    }

    public void setTgTtKienTk(double tgTtKienTk) {
        this.tgTtKienTk = tgTtKienTk;
    }

    public double getTgFmKienTk() {
        return tgFmKienTk;
    }

    public void setTgFmKienTk(double tgFmKienTk) {
        this.tgFmKienTk = tgFmKienTk;
    }

    public double getTgMmKienTk() {
        return tgMmKienTk;
    }

    public void setTgMmKienTk(double tgMmKienTk) {
        this.tgMmKienTk = tgMmKienTk;
    }

    public double getTgLmKienTk() {
        return tgLmKienTk;
    }

    public void setTgLmKienTk(double tgLmKienTk) {
        this.tgLmKienTk = tgLmKienTk;
    }

    public double getTgNoiTinhKienTk() {
        return tgNoiTinhKienTk;
    }

    public void setTgNoiTinhKienTk(double tgNoiTinhKienTk) {
        this.tgNoiTinhKienTk = tgNoiTinhKienTk;
    }

    public double getTgNoiMienKienTk() {
        return tgNoiMienKienTk;
    }

    public void setTgNoiMienKienTk(double tgNoiMienKienTk) {
        this.tgNoiMienKienTk = tgNoiMienKienTk;
    }

    public double getTgLienMienKienTk() {
        return tgLienMienKienTk;
    }

    public void setTgLienMienKienTk(double tgLienMienKienTk) {
        this.tgLienMienKienTk = tgLienMienKienTk;
    }

    public double getTgTtTachKienTk() {
        return tgTtTachKienTk;
    }

    public void setTgTtTachKienTk(double tgTtTachKienTk) {
        this.tgTtTachKienTk = tgTtTachKienTk;
    }

    public double getTgFmTachKienTk() {
        return tgFmTachKienTk;
    }

    public void setTgFmTachKienTk(double tgFmTachKienTk) {
        this.tgFmTachKienTk = tgFmTachKienTk;
    }

    public double getTgMmTachKienTk() {
        return tgMmTachKienTk;
    }

    public void setTgMmTachKienTk(double tgMmTachKienTk) {
        this.tgMmTachKienTk = tgMmTachKienTk;
    }

    public double getTgLmTachKienTk() {
        return tgLmTachKienTk;
    }

    public void setTgLmTachKienTk(double tgLmTachKienTk) {
        this.tgLmTachKienTk = tgLmTachKienTk;
    }

    public double getTgNoiTinhTachKienTk() {
        return tgNoiTinhTachKienTk;
    }

    public void setTgNoiTinhTachKienTk(double tgNoiTinhTachKienTk) {
        this.tgNoiTinhTachKienTk = tgNoiTinhTachKienTk;
    }

    public double getTgNoiMienTachKienTk() {
        return tgNoiMienTachKienTk;
    }

    public void setTgNoiMienTachKienTk(double tgNoiMienTachKienTk) {
        this.tgNoiMienTachKienTk = tgNoiMienTachKienTk;
    }

    public double getTgLienMienTachKienTk() {
        return tgLienMienTachKienTk;
    }

    public void setTgLienMienTachKienTk(double tgLienMienTachKienTk) {
        this.tgLienMienTachKienTk = tgLienMienTachKienTk;
    }
}
