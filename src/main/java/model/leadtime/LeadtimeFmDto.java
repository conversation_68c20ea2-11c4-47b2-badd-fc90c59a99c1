package model.leadtime;

import lombok.*;

@AllArgsConstructor
@Data
public class LeadtimeFmDto {
    private String ngayBaoCao;
    private String maChiNhanh;
    private String maBuuCuc;
    private float tgFmAll;
    private float tgFmNhanh;
    private float tgFmTk;
    private float tgFmKienTk;
    private float tgFmTachKienTk;

    // số lượng đơn theo khâu fm
    private long slFmAll;
    private long slFmNhanh;
    private long slFmTk;
    private long slFmKienTk;
    private long slFmTachKienTk;

    public LeadtimeFmDto() {
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public float getTgFmAll() {
        return tgFmAll;
    }

    public void setTgFmAll(float tgFmAll) {
        this.tgFmAll = tgFmAll;
    }

    public float getTgFmNhanh() {
        return tgFmNhanh;
    }

    public void setTgFmNhanh(float tgFmNhanh) {
        this.tgFmNhanh = tgFmNhanh;
    }

    public float getTgFmTk() {
        return tgFmTk;
    }

    public void setTgFmTk(float tgFmTk) {
        this.tgFmTk = tgFmTk;
    }

    public float getTgFmKienTk() {
        return tgFmKienTk;
    }

    public void setTgFmKienTk(float tgFmKienTk) {
        this.tgFmKienTk = tgFmKienTk;
    }

    public float getTgFmTachKienTk() {
        return tgFmTachKienTk;
    }

    public void setTgFmTachKienTk(float tgFmTachKienTk) {
        this.tgFmTachKienTk = tgFmTachKienTk;
    }

    public long getSlFmAll() {
        return slFmAll;
    }

    public void setSlFmAll(long slFmAll) {
        this.slFmAll = slFmAll;
    }

    public long getSlFmNhanh() {
        return slFmNhanh;
    }

    public void setSlFmNhanh(long slFmNhanh) {
        this.slFmNhanh = slFmNhanh;
    }

    public long getSlFmTk() {
        return slFmTk;
    }

    public void setSlFmTk(long slFmTk) {
        this.slFmTk = slFmTk;
    }

    public long getSlFmKienTk() {
        return slFmKienTk;
    }

    public void setSlFmKienTk(long slFmKienTk) {
        this.slFmKienTk = slFmKienTk;
    }

    public long getSlFmTachKienTk() {
        return slFmTachKienTk;
    }

    public void setSlFmTachKienTk(long slFmTachKienTk) {
        this.slFmTachKienTk = slFmTachKienTk;
    }
}
