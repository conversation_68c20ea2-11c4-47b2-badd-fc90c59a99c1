package model.leadtime;

import lombok.*;


public class LeadTimeAllDto {
    private String ngayBaoCao; //key
    private String maChiNhanh; // key
    private String maBuuCuc;// key
    /////////////////////////
    private float tgTtAll;
    private float tgFmAll;
    private float tgMmAll;
    private float tgLmAll;
    private float tgNoiTinhAll;
    private float tgNoiMienAll;
    private float tgLienMienAll;
    /////////////////////////
    private float tgTtNhanh;
    private float tgFmNhanh;
    private float tgMmNhanh;
    private float tgLmNhanh;
    private float tgNoiTinhNhanh;
    private float tgNoiMienNhanh;
    private float tgLienMienNhanh;
    /////////////////////////
    private float tgTtTk;
    private float tgFmTk;
    private float tgMmTk;
    private float tgLmTk;
    private float tgNoiTinhTk;
    private float tgNoiMienTk;
    private float tgLienMienTk;
    /////////////////////////
    private float tgTtKienTk;
    private float tgFmKienTk;
    private float tgMmKienTk;
    private float tgLmKienTk;
    private float tgNoiTinhKienTk;
    private float tgNoiMienKienTk;
    private float tgLienMienKienTk;
    /////////////////////////
    private float tgTtTachKienTk;
    private float tgFmTachKienTk;
    private float tgMmTachKienTk;
    private float tgLmTachKienTk;
    private float tgNoiTinhTachKienTk;
    private float tgNoiMienTachKienTk;
    private float tgLienMienTachKienTk;

    // các cột số lượng
    private long slDonAll;
    private long slNoiTinhAll;
    private long slNoiMienAll;
    private long slLienMienAll;
    private long slTtNhanh;
    private long slMmNhanh;
    private long slLmNhanh;
    private long slNoiTinhNhanh;
    private long slNoiMienNhanh;
    private long slLienMienNhanh;
    private long slTtTk;
    private long slMmTk;
    private long slLmTk;
    private long slNoiTinhTk;
    private long slNoiMienTk;
    private long slLienMienTk;
    private long slTtKienTk;
    private long slMmKienTk;
    private long slLmKienTk;
    private long slNoiTinhKienTk;
    private long slNoiMienKienTk;
    private long slLienMienKienTk;
    private long slTtTachKienTk;
    private long slMmTachKienTk;
    private long slLmTachKienTk;
    private long slNoiTinhTachKienTk;
    private long slNoiMienTachKienTk;
    private long slLienMienTachKienTk;

    private long slFmAll;
    private long slFmNhanh;
    private long slFmTk;
    private long slFmKienTk;
    private long slFmTachKienTk;

    private float tgMmNoiMien;
    private float tgMmLienMien;

    public LeadTimeAllDto() {
    }

    public LeadTimeAllDto(String maChiNhanh, String maBuuCuc, float tgTtAll, float tgFmAll, float tgMmAll, float tgLmAll, float tgNoiTinhAll, float tgNoiMienAll, float tgLienMienAll, float tgTtNhanh, float tgFmNhanh, float tgMmNhanh, float tgLmNhanh, float tgNoiTinhNhanh, float tgNoiMienNhanh, float tgLienMienNhanh, float tgTtTk, float tgFmTk, float tgMmTk, float tgLmTk, float tgNoiTinhTk, float tgNoiMienTk, float tgLienMienTk, float tgTtKienTk, float tgFmKienTk, float tgMmKienTk, float tgLmKienTk, float tgNoiTinhKienTk, float tgNoiMienKienTk, float tgLienMienKienTk, float tgTtTachKienTk, float tgFmTachKienTk, float tgMmTachKienTk, float tgLmTachKienTk, float tgNoiTinhTachKienTk, float tgNoiMienTachKienTk, float tgLienMienTachKienTk) {
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.tgTtAll = tgTtAll;
        this.tgFmAll = tgFmAll;
        this.tgMmAll = tgMmAll;
        this.tgLmAll = tgLmAll;
        this.tgNoiTinhAll = tgNoiTinhAll;
        this.tgNoiMienAll = tgNoiMienAll;
        this.tgLienMienAll = tgLienMienAll;
        this.tgTtNhanh = tgTtNhanh;
        this.tgFmNhanh = tgFmNhanh;
        this.tgMmNhanh = tgMmNhanh;
        this.tgLmNhanh = tgLmNhanh;
        this.tgNoiTinhNhanh = tgNoiTinhNhanh;
        this.tgNoiMienNhanh = tgNoiMienNhanh;
        this.tgLienMienNhanh = tgLienMienNhanh;
        this.tgTtTk = tgTtTk;
        this.tgFmTk = tgFmTk;
        this.tgMmTk = tgMmTk;
        this.tgLmTk = tgLmTk;
        this.tgNoiTinhTk = tgNoiTinhTk;
        this.tgNoiMienTk = tgNoiMienTk;
        this.tgLienMienTk = tgLienMienTk;
        this.tgTtKienTk = tgTtKienTk;
        this.tgFmKienTk = tgFmKienTk;
        this.tgMmKienTk = tgMmKienTk;
        this.tgLmKienTk = tgLmKienTk;
        this.tgNoiTinhKienTk = tgNoiTinhKienTk;
        this.tgNoiMienKienTk = tgNoiMienKienTk;
        this.tgLienMienKienTk = tgLienMienKienTk;
        this.tgTtTachKienTk = tgTtTachKienTk;
        this.tgFmTachKienTk = tgFmTachKienTk;
        this.tgMmTachKienTk = tgMmTachKienTk;
        this.tgLmTachKienTk = tgLmTachKienTk;
        this.tgNoiTinhTachKienTk = tgNoiTinhTachKienTk;
        this.tgNoiMienTachKienTk = tgNoiMienTachKienTk;
        this.tgLienMienTachKienTk = tgLienMienTachKienTk;
    }

    public float getTgMmNoiMien() {
        return tgMmNoiMien;
    }

    public void setTgMmNoiMien(float tgMmNoiMien) {
        this.tgMmNoiMien = tgMmNoiMien;
    }

    public float getTgMmLienMien() {
        return tgMmLienMien;
    }

    public void setTgMmLienMien(float tgMmLienMien) {
        this.tgMmLienMien = tgMmLienMien;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public float getTgTtAll() {
        return tgTtAll;
    }

    public void setTgTtAll(float tgTtAll) {
        this.tgTtAll = tgTtAll;
    }

    public float getTgFmAll() {
        return tgFmAll;
    }

    public void setTgFmAll(float tgFmAll) {
        this.tgFmAll = tgFmAll;
    }

    public float getTgMmAll() {
        return tgMmAll;
    }

    public void setTgMmAll(float tgMmAll) {
        this.tgMmAll = tgMmAll;
    }

    public float getTgLmAll() {
        return tgLmAll;
    }

    public void setTgLmAll(float tgLmAll) {
        this.tgLmAll = tgLmAll;
    }

    public float getTgNoiTinhAll() {
        return tgNoiTinhAll;
    }

    public void setTgNoiTinhAll(float tgNoiTinhAll) {
        this.tgNoiTinhAll = tgNoiTinhAll;
    }

    public float getTgNoiMienAll() {
        return tgNoiMienAll;
    }

    public void setTgNoiMienAll(float tgNoiMienAll) {
        this.tgNoiMienAll = tgNoiMienAll;
    }

    public float getTgLienMienAll() {
        return tgLienMienAll;
    }

    public void setTgLienMienAll(float tgLienMienAll) {
        this.tgLienMienAll = tgLienMienAll;
    }

    public float getTgTtNhanh() {
        return tgTtNhanh;
    }

    public void setTgTtNhanh(float tgTtNhanh) {
        this.tgTtNhanh = tgTtNhanh;
    }

    public float getTgFmNhanh() {
        return tgFmNhanh;
    }

    public void setTgFmNhanh(float tgFmNhanh) {
        this.tgFmNhanh = tgFmNhanh;
    }

    public float getTgMmNhanh() {
        return tgMmNhanh;
    }

    public void setTgMmNhanh(float tgMmNhanh) {
        this.tgMmNhanh = tgMmNhanh;
    }

    public float getTgLmNhanh() {
        return tgLmNhanh;
    }

    public void setTgLmNhanh(float tgLmNhanh) {
        this.tgLmNhanh = tgLmNhanh;
    }

    public float getTgNoiTinhNhanh() {
        return tgNoiTinhNhanh;
    }

    public void setTgNoiTinhNhanh(float tgNoiTinhNhanh) {
        this.tgNoiTinhNhanh = tgNoiTinhNhanh;
    }

    public float getTgNoiMienNhanh() {
        return tgNoiMienNhanh;
    }

    public void setTgNoiMienNhanh(float tgNoiMienNhanh) {
        this.tgNoiMienNhanh = tgNoiMienNhanh;
    }

    public float getTgLienMienNhanh() {
        return tgLienMienNhanh;
    }

    public void setTgLienMienNhanh(float tgLienMienNhanh) {
        this.tgLienMienNhanh = tgLienMienNhanh;
    }

    public float getTgTtTk() {
        return tgTtTk;
    }

    public void setTgTtTk(float tgTtTk) {
        this.tgTtTk = tgTtTk;
    }

    public float getTgFmTk() {
        return tgFmTk;
    }

    public void setTgFmTk(float tgFmTk) {
        this.tgFmTk = tgFmTk;
    }

    public float getTgMmTk() {
        return tgMmTk;
    }

    public void setTgMmTk(float tgMmTk) {
        this.tgMmTk = tgMmTk;
    }

    public float getTgLmTk() {
        return tgLmTk;
    }

    public void setTgLmTk(float tgLmTk) {
        this.tgLmTk = tgLmTk;
    }

    public float getTgNoiTinhTk() {
        return tgNoiTinhTk;
    }

    public void setTgNoiTinhTk(float tgNoiTinhTk) {
        this.tgNoiTinhTk = tgNoiTinhTk;
    }

    public float getTgNoiMienTk() {
        return tgNoiMienTk;
    }

    public void setTgNoiMienTk(float tgNoiMienTk) {
        this.tgNoiMienTk = tgNoiMienTk;
    }

    public float getTgLienMienTk() {
        return tgLienMienTk;
    }

    public void setTgLienMienTk(float tgLienMienTk) {
        this.tgLienMienTk = tgLienMienTk;
    }

    public float getTgTtKienTk() {
        return tgTtKienTk;
    }

    public void setTgTtKienTk(float tgTtKienTk) {
        this.tgTtKienTk = tgTtKienTk;
    }

    public float getTgFmKienTk() {
        return tgFmKienTk;
    }

    public void setTgFmKienTk(float tgFmKienTk) {
        this.tgFmKienTk = tgFmKienTk;
    }

    public float getTgMmKienTk() {
        return tgMmKienTk;
    }

    public void setTgMmKienTk(float tgMmKienTk) {
        this.tgMmKienTk = tgMmKienTk;
    }

    public float getTgLmKienTk() {
        return tgLmKienTk;
    }

    public void setTgLmKienTk(float tgLmKienTk) {
        this.tgLmKienTk = tgLmKienTk;
    }

    public float getTgNoiTinhKienTk() {
        return tgNoiTinhKienTk;
    }

    public void setTgNoiTinhKienTk(float tgNoiTinhKienTk) {
        this.tgNoiTinhKienTk = tgNoiTinhKienTk;
    }

    public float getTgNoiMienKienTk() {
        return tgNoiMienKienTk;
    }

    public void setTgNoiMienKienTk(float tgNoiMienKienTk) {
        this.tgNoiMienKienTk = tgNoiMienKienTk;
    }

    public float getTgLienMienKienTk() {
        return tgLienMienKienTk;
    }

    public void setTgLienMienKienTk(float tgLienMienKienTk) {
        this.tgLienMienKienTk = tgLienMienKienTk;
    }

    public float getTgTtTachKienTk() {
        return tgTtTachKienTk;
    }

    public void setTgTtTachKienTk(float tgTtTachKienTk) {
        this.tgTtTachKienTk = tgTtTachKienTk;
    }

    public float getTgFmTachKienTk() {
        return tgFmTachKienTk;
    }

    public void setTgFmTachKienTk(float tgFmTachKienTk) {
        this.tgFmTachKienTk = tgFmTachKienTk;
    }

    public float getTgMmTachKienTk() {
        return tgMmTachKienTk;
    }

    public void setTgMmTachKienTk(float tgMmTachKienTk) {
        this.tgMmTachKienTk = tgMmTachKienTk;
    }

    public float getTgLmTachKienTk() {
        return tgLmTachKienTk;
    }

    public void setTgLmTachKienTk(float tgLmTachKienTk) {
        this.tgLmTachKienTk = tgLmTachKienTk;
    }

    public float getTgNoiTinhTachKienTk() {
        return tgNoiTinhTachKienTk;
    }

    public void setTgNoiTinhTachKienTk(float tgNoiTinhTachKienTk) {
        this.tgNoiTinhTachKienTk = tgNoiTinhTachKienTk;
    }

    public float getTgNoiMienTachKienTk() {
        return tgNoiMienTachKienTk;
    }

    public void setTgNoiMienTachKienTk(float tgNoiMienTachKienTk) {
        this.tgNoiMienTachKienTk = tgNoiMienTachKienTk;
    }

    public float getTgLienMienTachKienTk() {
        return tgLienMienTachKienTk;
    }

    public void setTgLienMienTachKienTk(float tgLienMienTachKienTk) {
        this.tgLienMienTachKienTk = tgLienMienTachKienTk;
    }

    public long getSlDonAll() {
        return slDonAll;
    }

    public void setSlDonAll(long slDonAll) {
        this.slDonAll = slDonAll;
    }

    public long getSlNoiTinhAll() {
        return slNoiTinhAll;
    }

    public void setSlNoiTinhAll(long slNoiTinhAll) {
        this.slNoiTinhAll = slNoiTinhAll;
    }

    public long getSlNoiMienAll() {
        return slNoiMienAll;
    }

    public void setSlNoiMienAll(long slNoiMienAll) {
        this.slNoiMienAll = slNoiMienAll;
    }

    public long getSlLienMienAll() {
        return slLienMienAll;
    }

    public void setSlLienMienAll(long slLienMienAll) {
        this.slLienMienAll = slLienMienAll;
    }

    public long getSlTtNhanh() {
        return slTtNhanh;
    }

    public void setSlTtNhanh(long slTtNhanh) {
        this.slTtNhanh = slTtNhanh;
    }

    public long getSlMmNhanh() {
        return slMmNhanh;
    }

    public void setSlMmNhanh(long slMmNhanh) {
        this.slMmNhanh = slMmNhanh;
    }

    public long getSlLmNhanh() {
        return slLmNhanh;
    }

    public void setSlLmNhanh(long slLmNhanh) {
        this.slLmNhanh = slLmNhanh;
    }

    public long getSlNoiTinhNhanh() {
        return slNoiTinhNhanh;
    }

    public void setSlNoiTinhNhanh(long slNoiTinhNhanh) {
        this.slNoiTinhNhanh = slNoiTinhNhanh;
    }

    public long getSlNoiMienNhanh() {
        return slNoiMienNhanh;
    }

    public void setSlNoiMienNhanh(long slNoiMienNhanh) {
        this.slNoiMienNhanh = slNoiMienNhanh;
    }

    public long getSlLienMienNhanh() {
        return slLienMienNhanh;
    }

    public void setSlLienMienNhanh(long slLienMienNhanh) {
        this.slLienMienNhanh = slLienMienNhanh;
    }

    public long getSlTtTk() {
        return slTtTk;
    }

    public void setSlTtTk(long slTtTk) {
        this.slTtTk = slTtTk;
    }

    public long getSlMmTk() {
        return slMmTk;
    }

    public void setSlMmTk(long slMmTk) {
        this.slMmTk = slMmTk;
    }

    public long getSlLmTk() {
        return slLmTk;
    }

    public void setSlLmTk(long slLmTk) {
        this.slLmTk = slLmTk;
    }

    public long getSlNoiTinhTk() {
        return slNoiTinhTk;
    }

    public void setSlNoiTinhTk(long slNoiTinhTk) {
        this.slNoiTinhTk = slNoiTinhTk;
    }

    public long getSlNoiMienTk() {
        return slNoiMienTk;
    }

    public void setSlNoiMienTk(long slNoiMienTk) {
        this.slNoiMienTk = slNoiMienTk;
    }

    public long getSlLienMienTk() {
        return slLienMienTk;
    }

    public void setSlLienMienTk(long slLienMienTk) {
        this.slLienMienTk = slLienMienTk;
    }

    public long getSlTtKienTk() {
        return slTtKienTk;
    }

    public void setSlTtKienTk(long slTtKienTk) {
        this.slTtKienTk = slTtKienTk;
    }

    public long getSlMmKienTk() {
        return slMmKienTk;
    }

    public void setSlMmKienTk(long slMmKienTk) {
        this.slMmKienTk = slMmKienTk;
    }

    public long getSlLmKienTk() {
        return slLmKienTk;
    }

    public void setSlLmKienTk(long slLmKienTk) {
        this.slLmKienTk = slLmKienTk;
    }

    public long getSlNoiTinhKienTk() {
        return slNoiTinhKienTk;
    }

    public void setSlNoiTinhKienTk(long slNoiTinhKienTk) {
        this.slNoiTinhKienTk = slNoiTinhKienTk;
    }

    public long getSlNoiMienKienTk() {
        return slNoiMienKienTk;
    }

    public void setSlNoiMienKienTk(long slNoiMienKienTk) {
        this.slNoiMienKienTk = slNoiMienKienTk;
    }

    public long getSlLienMienKienTk() {
        return slLienMienKienTk;
    }

    public void setSlLienMienKienTk(long slLienMienKienTk) {
        this.slLienMienKienTk = slLienMienKienTk;
    }

    public long getSlTtTachKienTk() {
        return slTtTachKienTk;
    }

    public void setSlTtTachKienTk(long slTtTachKienTk) {
        this.slTtTachKienTk = slTtTachKienTk;
    }

    public long getSlMmTachKienTk() {
        return slMmTachKienTk;
    }

    public void setSlMmTachKienTk(long slMmTachKienTk) {
        this.slMmTachKienTk = slMmTachKienTk;
    }

    public long getSlLmTachKienTk() {
        return slLmTachKienTk;
    }

    public void setSlLmTachKienTk(long slLmTachKienTk) {
        this.slLmTachKienTk = slLmTachKienTk;
    }

    public long getSlNoiTinhTachKienTk() {
        return slNoiTinhTachKienTk;
    }

    public void setSlNoiTinhTachKienTk(long slNoiTinhTachKienTk) {
        this.slNoiTinhTachKienTk = slNoiTinhTachKienTk;
    }

    public long getSlNoiMienTachKienTk() {
        return slNoiMienTachKienTk;
    }

    public void setSlNoiMienTachKienTk(long slNoiMienTachKienTk) {
        this.slNoiMienTachKienTk = slNoiMienTachKienTk;
    }

    public long getSlLienMienTachKienTk() {
        return slLienMienTachKienTk;
    }

    public void setSlLienMienTachKienTk(long slLienMienTachKienTk) {
        this.slLienMienTachKienTk = slLienMienTachKienTk;
    }

    public long getSlFmAll() {
        return slFmAll;
    }

    public void setSlFmAll(long slFmAll) {
        this.slFmAll = slFmAll;
    }

    public long getSlFmNhanh() {
        return slFmNhanh;
    }

    public void setSlFmNhanh(long slFmNhanh) {
        this.slFmNhanh = slFmNhanh;
    }

    public long getSlFmTk() {
        return slFmTk;
    }

    public void setSlFmTk(long slFmTk) {
        this.slFmTk = slFmTk;
    }

    public long getSlFmKienTk() {
        return slFmKienTk;
    }

    public void setSlFmKienTk(long slFmKienTk) {
        this.slFmKienTk = slFmKienTk;
    }

    public long getSlFmTachKienTk() {
        return slFmTachKienTk;
    }

    public void setSlFmTachKienTk(long slFmTachKienTk) {
        this.slFmTachKienTk = slFmTachKienTk;
    }
}
