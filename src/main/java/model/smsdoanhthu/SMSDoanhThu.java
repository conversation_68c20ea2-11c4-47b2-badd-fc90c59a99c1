package model.smsdoanhthu;

import java.util.Objects;

import static utils.Utils.roundDoubleNum;

public class SMSDoanhThu {
    private String ngayBaoCao;
    private String maChiNhanh;
    private String maBuuCuc;

    //1. san luong
    private Long slNgay;
    private Long slLKThang;
    private Long slCKThang;
    private Double slTTCKThang;
    private Double slTTTBNThang;
    private Long slCKNam;
    private Double slTTCKNam;
    private Double slTTTBNNam;

    //2. doanh thu
    private Double dtNgay;
    private Double dtLKThang;
    private Double dtKeHoach;
    private Double tiLeHoanThanh;
    private Double tienDoDT;
    private Double dtCKThang; //Không tính doanh thu của mã khách hàng = PKDQT382
    private Double dtTTCKThang; //Không tính doanh thu của mã khách hàng = PKDQT382
    private Double dtCKNam; //Không tính doanh thu của mã khách hàng = PKDQT382
    private Double dtTTCKNam; //Không tính doanh thu của mã khách hàng = PKDQT382

    //2.1. Nhom CP(CPTN + QT)
    // Tham chieu tu man bao cao tong hop doanh thu
    // NonCOD + COD + EXP
    private Double dtNgayCP;
    private Double dtLKThangCP;
    private Double dtKeHoachCP;
    private Double tiLeHoanThanhKHCP;
    private Double tienDoDTCP;
    private Double dtCKThangCP;
    private Double dtTTCKThangCP;
    private Double dtTTTBNCKThangCP;
    private Double dtCKNamCP;
    private Double dtTTCKNamCP;
    private Double dtTTTBNCKNamCP;

    //2.2. Nhom kho van + fwd
    //VT+KHo
    private Double dtNgayVTKho;
    private Double dtLKThangVTKho;
    private Double dtKeHoachVTKho;
    private Double tlhtKeHoachVTKho;
    private Double tienDoDTVTKho;
    private Double dtCKThangVTKho;
    private Double dtTTCKThangVTKho;
    private Double dtCKNamVTKho;
    private Double dtTTCKNamVTKho;

    //2.3 TMUT -> MaKH = PKDQT382
    private Double dtNgayTMUT;
    private Double dtLKThangTMUT;
    private Double dtKeHoachTMUT;
    private Double tlhtKeHoachTMUT;
    private Double tienDoDTTMUT;

    public SMSDoanhThu() {
    }

    public SMSDoanhThu(String ngayBaoCao, String maChiNhanh, String maBuuCuc, Long slCPNgay, Long slCPLKThang, Long slCPLKCungKyThangTruoc,
                       Double slTBNThang, Double slTBNThangTruoc, Long slCPLKCungKyThangNamTruoc, Double slTBNCKThangNamTruoc) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.slNgay = slCPNgay;
        this.slLKThang = slCPLKThang;

        this.slCKThang = caculateChenhLechCK(slCPLKThang.doubleValue(), slCPLKCungKyThangTruoc.doubleValue()).longValue();
        this.slTTCKThang = devideSubstractionByNum(slCPLKThang.doubleValue(), slCPLKCungKyThangTruoc.doubleValue());
        this.slTTTBNThang = devideSubstractionByNum(slTBNThang, slTBNThangTruoc);

        this.slCKNam = caculateChenhLechCK(slCPLKThang.doubleValue(), slCPLKCungKyThangNamTruoc.doubleValue()).longValue();
        this.slTTCKNam = devideSubstractionByNum(slCPLKThang.doubleValue(), slCPLKCungKyThangNamTruoc.doubleValue());
        this.slTTTBNNam = devideSubstractionByNum(slTBNThang, slTBNCKThangNamTruoc);

    }

    /** Tong doanh thu thang hien tai **/
    public SMSDoanhThu(String ngayBaoCao, String maChiNhanh, String maBuuCuc, Double dtNgay, Double dtLKThang, Double dtKeHoachLK, Double dtKeHoach) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.dtNgay = roundDoubleNum(dtNgay);
        this.dtLKThang = roundDoubleNum(dtLKThang);
        this.dtKeHoach = roundDoubleNum(dtKeHoach);
        this.tiLeHoanThanh = devideNumByNum(dtLKThang, dtKeHoach);
        this.tienDoDT = caculateChenhLechCK(dtLKThang, dtKeHoachLK);
    }

    /** Tong doanh thu ck thang, nam truoc **/
    public SMSDoanhThu(String ngayBaoCao, String maChiNhanh, String maBuuCuc, Double dtLKThang, Double dtLKCKThangTruoc, Double dtLKCKThangNamTruoc, String type) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.dtCKThang = caculateChenhLechCK(dtLKThang, dtLKCKThangTruoc);
        this.dtTTCKThang = devideSubstractionByNum(dtLKThang, dtLKCKThangTruoc);
        this.dtCKNam = caculateChenhLechCK(dtLKThang, dtLKCKThangNamTruoc);
        this.dtTTCKNam = devideSubstractionByNum(dtLKThang, dtLKCKThangNamTruoc);

    }

    /** CP **/
    public SMSDoanhThu(String ngayBaoCao, String maChiNhanh, String maBuuCuc,
                       Double dtNgayCP, Double dtLKThangCP, Double dtKeHoachLKCP, Double dtKeHoachCP, Double dtLKCKThangTruocCP,
                       Double dtTBNThangCP, Double dtTBNThangTruocCP,
                       Double dtLKCKThangNamTruocCP, Double dtTBNCKThangNamTruocCP) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.dtNgayCP = (dtNgayCP == null) ? 0 : roundDoubleNum(dtNgayCP);
        this.dtLKThangCP = (dtLKThangCP == null) ? 0 : roundDoubleNum(dtLKThangCP);
        this.dtKeHoachCP = (dtKeHoachCP == null) ? 0 : roundDoubleNum(dtKeHoachCP);
        this.tiLeHoanThanhKHCP = devideNumByNum(dtLKThangCP, dtKeHoachCP);
        this.tienDoDTCP = caculateChenhLechCK(dtLKThangCP, dtKeHoachLKCP);
        this.dtCKThangCP = caculateChenhLechCK(dtLKThangCP, dtLKCKThangTruocCP);
        this.dtTTCKThangCP = devideSubstractionByNum(dtLKThangCP, dtLKCKThangTruocCP);
        this.dtTTTBNCKThangCP = devideSubstractionByNum(dtTBNThangCP, dtTBNThangTruocCP);

        this.dtCKNamCP = caculateChenhLechCK(dtLKThangCP, dtLKCKThangNamTruocCP);
        this.dtTTCKNamCP = devideSubstractionByNum(dtLKThangCP, dtLKCKThangNamTruocCP);
        this.dtTTTBNCKNamCP = devideSubstractionByNum(dtTBNThangCP, dtTBNCKThangNamTruocCP);
    }

    /** VT + Kho **/
    public SMSDoanhThu(String ngayBaoCao, String maChiNhanh, String maBuuCuc, Double dtNgayVTKho, Double dtLKThangVTKho, Double dtKeHoachLKVTKho, Double dtKeHoachVTKho, Double dtLKCKThangVTKho,
                       Double dtLKCKThangNamTruocVTKho) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.dtNgayVTKho = (dtNgayVTKho == null) ? 0 : roundDoubleNum(dtNgayVTKho);
        this.dtLKThangVTKho = (dtLKThangVTKho == null) ? 0 : roundDoubleNum(dtLKThangVTKho);
        this.dtKeHoachVTKho = (dtKeHoachVTKho == null) ? 0 : roundDoubleNum(dtKeHoachVTKho);
        this.tlhtKeHoachVTKho = devideNumByNum(dtLKThangVTKho, dtKeHoachVTKho);
        this.tienDoDTVTKho = caculateChenhLechCK(dtLKThangVTKho, dtKeHoachLKVTKho);
        this.dtCKThangVTKho = caculateChenhLechCK(dtLKThangVTKho, dtLKCKThangVTKho);
        this.dtTTCKThangVTKho = devideSubstractionByNum(dtLKThangVTKho, dtLKCKThangVTKho);
        this.dtCKNamVTKho = caculateChenhLechCK(dtLKThangVTKho, dtLKCKThangNamTruocVTKho);
        this.dtTTCKNamVTKho = devideSubstractionByNum(dtLKThangVTKho, dtLKCKThangNamTruocVTKho);
    }

    /** TMUT **/
    public SMSDoanhThu(String ngayBaoCao, String type, String maChiNhanh, String maBuuCuc, Double dtNgayTMUT, Double dtLKThangTMUT, Double dtKeHoachLKTMUT, Double dtKeHoachTMUT) {
        this.ngayBaoCao = ngayBaoCao;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.dtNgayTMUT = (dtNgayTMUT == null) ? 0 : roundDoubleNum(dtNgayTMUT);
        this.dtLKThangTMUT = (dtLKThangTMUT == null) ? 0 : roundDoubleNum(dtLKThangTMUT);
        this.dtKeHoachTMUT = (dtKeHoachTMUT == null) ? 0 : roundDoubleNum(dtKeHoachTMUT);
        this.tlhtKeHoachTMUT = devideNumByNum(dtLKThangTMUT, dtKeHoachTMUT);
        this.tienDoDTTMUT = caculateChenhLechCK(dtLKThangTMUT, dtKeHoachLKTMUT);
    }

    public Double devideNumByNum(Double numberOne, Double numberTwo) {
        Double result = (double) 0;
        if (!Objects.isNull(numberOne) && !Objects.isNull(numberTwo)) {
            result = (numberOne == 0 || numberTwo == 0) ? 0 :
                    roundDoubleNum(numberOne*1.0 / numberTwo * 100);
        }
        return result;
    }

    public Double devideSubstractionByNum(Double numberOne, Double numberTwo) {
        Double result = (double) 0;
        if (!Objects.isNull(numberOne) && !Objects.isNull(numberTwo)) {
            result = ((numberOne - numberTwo) == 0 || numberTwo == 0) ? 0 :
                    roundDoubleNum((numberOne*1.0 - numberTwo*1.0) / numberTwo * 100);
        }
        return result;
    }

    public Double caculateChenhLechCK(Double numberOne, Double numberTwo) {
        Double result = (double) 0;
        if (!Objects.isNull(numberOne) && !Objects.isNull(numberTwo)) {
            result =  roundDoubleNum(numberOne - numberTwo);
        }
        return result;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public Long getSlNgay() {
        return slNgay;
    }

    public void setSlNgay(Long slNgay) {
        this.slNgay = slNgay;
    }

    public Long getSlLKThang() {
        return slLKThang;
    }

    public void setSlLKThang(Long slLKThang) {
        this.slLKThang = slLKThang;
    }

    public Long getSlCKThang() {
        return slCKThang;
    }

    public void setSlCKThang(Long slCKThang) {
        this.slCKThang = slCKThang;
    }

    public Double getSlTTCKThang() {
        return slTTCKThang;
    }

    public void setSlTTCKThang(Double slTTCKThang) {
        this.slTTCKThang = slTTCKThang;
    }

    public Double getSlTTTBNThang() {
        return slTTTBNThang;
    }

    public void setSlTTTBNThang(Double slTTTBNThang) {
        this.slTTTBNThang = slTTTBNThang;
    }

    public Long getSlCKNam() {
        return slCKNam;
    }

    public void setSlCKNam(Long slCKNam) {
        this.slCKNam = slCKNam;
    }

    public Double getSlTTCKNam() {
        return slTTCKNam;
    }

    public void setSlTTCKNam(Double slTTCKNam) {
        this.slTTCKNam = slTTCKNam;
    }

    public Double getSlTTTBNNam() {
        return slTTTBNNam;
    }

    public void setSlTTTBNNam(Double slTTTBNNam) {
        this.slTTTBNNam = slTTTBNNam;
    }

    public Double getDtNgay() {
        return dtNgay;
    }

    public void setDtNgay(Double dtNgay) {
        this.dtNgay = dtNgay;
    }

    public Double getDtLKThang() {
        return dtLKThang;
    }

    public void setDtLKThang(Double dtLKThang) {
        this.dtLKThang = dtLKThang;
    }

    public Double getDtKeHoach() {
        return dtKeHoach;
    }

    public void setDtKeHoach(Double dtKeHoach) {
        this.dtKeHoach = dtKeHoach;
    }

    public Double getTiLeHoanThanh() {
        return tiLeHoanThanh;
    }

    public void setTiLeHoanThanh(Double tiLeHoanThanh) {
        this.tiLeHoanThanh = tiLeHoanThanh;
    }

    public Double getTienDoDT() {
        return tienDoDT;
    }

    public void setTienDoDT(Double tienDoDT) {
        this.tienDoDT = tienDoDT;
    }

    public Double getDtCKThang() {
        return dtCKThang;
    }

    public void setDtCKThang(Double dtCKThang) {
        this.dtCKThang = dtCKThang;
    }

    public Double getDtTTCKThang() {
        return dtTTCKThang;
    }

    public void setDtTTCKThang(Double dtTTCKThang) {
        this.dtTTCKThang = dtTTCKThang;
    }

    public Double getDtCKNam() {
        return dtCKNam;
    }

    public void setDtCKNam(Double dtCKNam) {
        this.dtCKNam = dtCKNam;
    }

    public Double getDtTTCKNam() {
        return dtTTCKNam;
    }

    public void setDtTTCKNam(Double dtTTCKNam) {
        this.dtTTCKNam = dtTTCKNam;
    }

    public Double getDtNgayCP() {
        return dtNgayCP;
    }

    public void setDtNgayCP(Double dtNgayCP) {
        this.dtNgayCP = dtNgayCP;
    }

    public Double getDtLKThangCP() {
        return dtLKThangCP;
    }

    public void setDtLKThangCP(Double dtLKThangCP) {
        this.dtLKThangCP = dtLKThangCP;
    }

    public Double getDtKeHoachCP() {
        return dtKeHoachCP;
    }

    public void setDtKeHoachCP(Double dtKeHoachLKCP) {
        this.dtKeHoachCP = dtKeHoachLKCP;
    }

    public Double getTiLeHoanThanhKHCP() {
        return tiLeHoanThanhKHCP;
    }

    public void setTiLeHoanThanhKHCP(Double tiLeHoanThanhKHCP) {
        this.tiLeHoanThanhKHCP = tiLeHoanThanhKHCP;
    }

    public Double getTienDoDTCP() {
        return tienDoDTCP;
    }

    public void setTienDoDTCP(Double tienDoDTCP) {
        this.tienDoDTCP = tienDoDTCP;
    }

    public Double getDtCKThangCP() {
        return dtCKThangCP;
    }

    public void setDtCKThangCP(Double dtCKThangCP) {
        this.dtCKThangCP = dtCKThangCP;
    }

    public Double getDtTTCKThangCP() {
        return dtTTCKThangCP;
    }

    public void setDtTTCKThangCP(Double dtTTCKThangCP) {
        this.dtTTCKThangCP = dtTTCKThangCP;
    }

    public Double getDtTTTBNCKThangCP() {
        return dtTTTBNCKThangCP;
    }

    public void setDtTTTBNCKThangCP(Double dtTTTBNCKThangCP) {
        this.dtTTTBNCKThangCP = dtTTTBNCKThangCP;
    }

    public Double getDtCKNamCP() {
        return dtCKNamCP;
    }

    public void setDtCKNamCP(Double dtCKNamCP) {
        this.dtCKNamCP = dtCKNamCP;
    }

    public Double getDtTTCKNamCP() {
        return dtTTCKNamCP;
    }

    public void setDtTTCKNamCP(Double dtTTCKNamCP) {
        this.dtTTCKNamCP = dtTTCKNamCP;
    }

    public Double getDtTTTBNCKNamCP() {
        return dtTTTBNCKNamCP;
    }

    public void setDtTTTBNCKNamCP(Double dtTTTBNCKNamCP) {
        this.dtTTTBNCKNamCP = dtTTTBNCKNamCP;
    }

    public Double getDtNgayVTKho() {
        return dtNgayVTKho;
    }

    public void setDtNgayVTKho(Double dtNgayVTKho) {
        this.dtNgayVTKho = dtNgayVTKho;
    }

    public Double getDtLKThangVTKho() {
        return dtLKThangVTKho;
    }

    public void setDtLKThangVTKho(Double dtLKThangVTKho) {
        this.dtLKThangVTKho = dtLKThangVTKho;
    }

    public Double getDtKeHoachVTKho() {
        return dtKeHoachVTKho;
    }

    public void setDtKeHoachVTKho(Double dtKeHoachVTKho) {
        this.dtKeHoachVTKho = dtKeHoachVTKho;
    }

    public Double getTlhtKeHoachVTKho() {
        return tlhtKeHoachVTKho;
    }

    public void setTlhtKeHoachVTKho(Double tlhtKeHoachVTKho) {
        this.tlhtKeHoachVTKho = tlhtKeHoachVTKho;
    }

    public Double getTienDoDTVTKho() {
        return tienDoDTVTKho;
    }

    public void setTienDoDTVTKho(Double tienDoDTVTKho) {
        this.tienDoDTVTKho = tienDoDTVTKho;
    }

    public Double getDtCKThangVTKho() {
        return dtCKThangVTKho;
    }

    public void setDtCKThangVTKho(Double dtCKThangVTKho) {
        this.dtCKThangVTKho = dtCKThangVTKho;
    }

    public Double getDtTTCKThangVTKho() {
        return dtTTCKThangVTKho;
    }

    public void setDtTTCKThangVTKho(Double dtTTCKThangVTKho) {
        this.dtTTCKThangVTKho = dtTTCKThangVTKho;
    }

    public Double getDtCKNamVTKho() {
        return dtCKNamVTKho;
    }

    public void setDtCKNamVTKho(Double dtCKNamVTKho) {
        this.dtCKNamVTKho = dtCKNamVTKho;
    }

    public Double getDtTTCKNamVTKho() {
        return dtTTCKNamVTKho;
    }

    public void setDtTTCKNamVTKho(Double dtTTCKNamVTKho) {
        this.dtTTCKNamVTKho = dtTTCKNamVTKho;
    }

    public Double getDtNgayTMUT() {
        return dtNgayTMUT;
    }

    public void setDtNgayTMUT(Double dtNgayTMUT) {
        this.dtNgayTMUT = dtNgayTMUT;
    }

    public Double getDtLKThangTMUT() {
        return dtLKThangTMUT;
    }

    public void setDtLKThangTMUT(Double dtLKThangTMUT) {
        this.dtLKThangTMUT = dtLKThangTMUT;
    }

    public Double getDtKeHoachTMUT() {
        return dtKeHoachTMUT;
    }

    public void setDtKeHoachTMUT(Double dtKeHoachLKTMUT) {
        this.dtKeHoachTMUT = dtKeHoachLKTMUT;
    }

    public Double getTlhtKeHoachTMUT() {
        return tlhtKeHoachTMUT;
    }

    public void setTlhtKeHoachTMUT(Double tlhtKeHoachTMUT) {
        this.tlhtKeHoachTMUT = tlhtKeHoachTMUT;
    }

    public Double getTienDoDTTMUT() {
        return tienDoDTTMUT;
    }

    public void setTienDoDTTMUT(Double tienDoDTTMUT) {
        this.tienDoDTTMUT = tienDoDTTMUT;
    }
}
