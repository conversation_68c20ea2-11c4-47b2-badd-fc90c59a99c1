package jobs;

import configs.Configuration;
import constant.Const;
import jobs.process.cldv_topcn.TopChiNhanhProcess;
import jobs.process.doanhthusanluong.DoanhThuSanLuongProccess;
import jobs.process.leadtime.LeadTimeProcess;
import jobs.process.phan_loai_cuoc_goi_tieu_cuc.PhanLoaiCuocGoiTieuCuc;
import jobs.process.smsdoanhthu.SMSDoanhThuProccess;

import jobs.process.phat_hien_don_gach_ao.PhatHienDonGachAo;


import static configs.ConfigurationFactory.getConfigInstance;

public class Main {
    private static Configuration config;

    public static void main(String[] args) throws ClassNotFoundException {
        config = getConfigInstance();
        //todo chỗ này config chạy job theo switch case với các báo khác
        String mode = config.getConfig("MODE_JOB");
        System.out.println("RUN MODE : " + mode);
        switch (mode) {
            case Const.LEAD_TIME:
                LeadTimeProcess leadTime = new LeadTimeProcess();
                leadTime.initData();
                leadTime.process();
                break;
            case Const.MIDDLE_MILE:
                System.out.println("MIDDLE_MILE");
                break;
            case Const.TOP_CN:
                TopChiNhanhProcess topChiNhanhProcess = new TopChiNhanhProcess();
                topChiNhanhProcess.initData();
                topChiNhanhProcess.process();
                break;

            case Const.PHAN_LOAI_CUOC_GOI:
                PhanLoaiCuocGoiTieuCuc phanLoaiProcess = new PhanLoaiCuocGoiTieuCuc();
                phanLoaiProcess.initData();
                phanLoaiProcess.process();
                break;
            case Const.PHAT_HIEN_DON_GACH_AO:
                PhatHienDonGachAo phatHienDonGachAo = new PhatHienDonGachAo();
                phatHienDonGachAo.initData();
                phatHienDonGachAo.process();
                break;
            case Const.SMS_DOANH_THU:
                SMSDoanhThuProccess smsDoanhThuProccess = new SMSDoanhThuProccess();
                smsDoanhThuProccess.initData();
                smsDoanhThuProccess.process();
                break;
            case Const.DOANH_THU_SAN_LUONG:
                DoanhThuSanLuongProccess doanhThuSanLuongProccess = new DoanhThuSanLuongProccess();
                doanhThuSanLuongProccess.initData();
                doanhThuSanLuongProccess.process();
                break;
            default:
                System.out.println("NOOOOOOOOOOOOO");
                break;
        }
    }
}
