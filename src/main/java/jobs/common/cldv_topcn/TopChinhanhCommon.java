package jobs.common.cldv_topcn;

import configs.Configuration;
import connection.PhoenixConnectionFactory;
import constant.Const;
import model.cldv_cn.KPI;
import model.cldv_cn.KhauModel;
import model.cldv_cn.LeadTimeModel;
import model.cldv_cn.TopModel;
import utils.AbstractDao;
import utils.DateTimeUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class TopChinhanhCommon extends AbstractDao {

    //Khau thu
    private final String tableKhauThuNgay = "TILE_KHAUTHU_TONGHOP_NGAY";
    private final String tableKhauThuThang = "TILE_KHAUTHU_TONGHOP_THANG";
    private final String maChiSoThuNgay = "tile-khauthu-ngay";
    private final String maChiSoThuThang = "tile-khauthu-thang";

    //Khau <PERSON>at-hoan
    private final String tblKhauPhatNgay = "TILE_KHAUPHAT_TONGHOP_NGAY";
    private final String tblKhauPhatThang = "TILE_KHAUPHAT_TONGHOP_NGAY";
    private final String maChiSoPhatNgay = "TILE_KHAUPHAT_CHITIET_NGAY";
    private final String maChiSoPhatThang = "TILE_KHAUPHAT_CHITIET_NGAY";

    //Leadtime
    private final String tblLeadTimeNgay = "dash_tct_leadtime_ngay";
    private final String tblLeadTimeThang = "dash_tct_leadtime_thang";

    public List<TopModel> calculateData(Configuration configuration, String ngayBaocao) {

        //Tông hợp data chi nhánh bưu cục khau thu
        List<KhauModel> khauThuTCAllList = new ArrayList<>();
        List<KhauModel> khauThuTCCNListNgay = khauThuTc(ngayBaocao, queryKhauThuTCNgayCN(ngayBaocao, tableKhauThuNgay, maChiSoThuNgay), 0);
        List<KhauModel> khauThuTCCNBCListNgay = khauThuTc(ngayBaocao, queryKhauThuTCNgayCNBC(ngayBaocao, tableKhauThuNgay, maChiSoThuNgay), 0);
        List<KhauModel> khauThuTCCNListThang = khauThuTc(ngayBaocao, queryKhauThuTCNgayCN(ngayBaocao, tableKhauThuThang, maChiSoThuThang), 1);
        List<KhauModel> khauThuTCCNBCListThang = khauThuTc(ngayBaocao, queryKhauThuTCNgayCNBC(ngayBaocao, tableKhauThuThang, maChiSoThuThang), 1);
        khauThuTCAllList.addAll(khauThuTCCNListNgay);
        khauThuTCAllList.addAll(khauThuTCCNBCListNgay);
        khauThuTCAllList.addAll(khauThuTCCNListThang);
        khauThuTCAllList.addAll(khauThuTCCNBCListThang);
        // Tổng hợp data chi nhánh bưu cục khau phát
        List<KhauModel> khauPhatAllList = new ArrayList<>();
        List<KhauModel> khauPhatCNListsNgay = khauPhatList(ngayBaocao, queryKhauPhatCN(ngayBaocao, tblKhauPhatNgay, maChiSoPhatNgay), 0);
        List<KhauModel> khauPhatCNBCListsNgay = khauPhatList(ngayBaocao, queryKhauPhatCNBC(ngayBaocao, tblKhauPhatNgay, maChiSoPhatNgay), 0);
        List<KhauModel> khauPhatCNListsThang = khauPhatList(ngayBaocao, queryKhauPhatCN(ngayBaocao, tblKhauPhatThang, maChiSoPhatThang), 1);
        List<KhauModel> khauPhatCNBCListsThang = khauPhatList(ngayBaocao, queryKhauPhatCNBC(ngayBaocao, tblKhauPhatThang, maChiSoPhatThang), 1);
        khauPhatAllList.addAll(khauPhatCNListsNgay);
        khauPhatAllList.addAll(khauPhatCNBCListsNgay);
        khauPhatAllList.addAll(khauPhatCNListsThang);
        khauPhatAllList.addAll(khauPhatCNBCListsThang);
        //LEAD TIME KPI PHAN DOAN
        List<LeadTimeModel> leadTimeAll = new ArrayList<>();
        List<LeadTimeModel> leadTimeNgayList = getListTime(configuration, ngayBaocao, tblLeadTimeNgay, 0);
        List<LeadTimeModel> leadTimeThangList = getListTime(configuration, ngayBaocao, tblLeadTimeThang, 1);
        leadTimeAll.addAll(leadTimeNgayList);
        leadTimeAll.addAll(leadTimeThangList);
        //List tong hop
        List<TopModel> topModelList = new ArrayList<>();
        // KPI ứng với từng top
        List<KPI> kpiNTDungGioList = getListKPI(configuration, DateTimeUtil.getDateFormatYYYYMM(ngayBaocao), "NTC_DG");
        List<KPI> kpiNTCList = getListKPI(configuration, DateTimeUtil.getDateFormatYYYYMM(ngayBaocao), "NTC");
        List<KPI> kpiPTCList = getListKPI(configuration, DateTimeUtil.getDateFormatYYYYMM(ngayBaocao), "GTC_TONG");
        List<KPI> kpiPTCDungGioList = getListKPI(configuration, DateTimeUtil.getDateFormatYYYYMM(ngayBaocao), "GTC_DG");
        List<KPI> kpiHoan = getListKPI(configuration, DateTimeUtil.getDateFormatYYYYMM(ngayBaocao), "TY_LE_HOAN");
        List<KPI> kpiNoiTinh = getListKPI(configuration, DateTimeUtil.getDateFormatYYYYMM(ngayBaocao), "TGTT_NOI_TINH");
        //process khau thu
        System.out.println("Start process khau thu");

        for (KhauModel item : khauThuTCAllList) {
            for (KPI kpi : kpiNTCList) {
                if (item != null && item.getMaChiNhanh().equals(kpi.getMaChiNhanh()) && item.getMaBuuCuc().equals(kpi.getMaBuucuc()) && item.getLoaiChiTieu().equals(kpi.getLoaiChiTieu())) {
                    float tlHoanThanh =kpi.getChiTieuGiao() == 0 ? 0 :  item.getTlThuTC() * 100 / kpi.getChiTieuGiao();
                    TopModel top = addTopModelToList(ngayBaocao, Const.THU_TC, item, kpi, item.getTlThuTC(), tlHoanThanh, 0);
                    topModelList.add(top);
                }
            }

            for (KPI kpi : kpiNTDungGioList) {
                if (item != null && item.getMaChiNhanh().equals(kpi.getMaChiNhanh()) && item.getMaBuuCuc().equals(kpi.getMaBuucuc()) && item.getLoaiChiTieu().equals(kpi.getLoaiChiTieu())) {
                    float tlHoanThanh =kpi.getChiTieuGiao() == 0 ? 0 :  item.getTlThuDG() * 100 / kpi.getChiTieuGiao();
                    TopModel top = addTopModelToList(ngayBaocao, Const.THU_TC_DG, item, kpi, item.getTlThuDG(), tlHoanThanh, 0);
                    topModelList.add(top);
                }
            }
        }
        System.out.println("Khau thu : " + topModelList.size());
        System.out.println("Finish process khau thu");
        // khau phat- hoan
        System.out.println("Start process khau phat - hoan ");

        for (KhauModel item : khauPhatAllList) {
            for (KPI kpi : kpiPTCList) {
                if (item != null && item.getMaChiNhanh().equals(kpi.getMaChiNhanh()) && item.getMaBuuCuc().equals(kpi.getMaBuucuc()) && item.getLoaiChiTieu().equals(kpi.getLoaiChiTieu())) {
                    float tlHoanThanh = kpi.getChiTieuGiao() == 0 ? 0 : item.getTlPhatTC() * 100 / kpi.getChiTieuGiao();
                    TopModel top = addTopModelToList(ngayBaocao, Const.PHAT_TC, item, kpi, item.getTlPhatTC(), tlHoanThanh, 0);
                    topModelList.add(top);
                }
            }

            for (KPI kpi : kpiPTCDungGioList) {
                if (item != null && item.getMaChiNhanh().equals(kpi.getMaChiNhanh()) && item.getMaBuuCuc().equals(kpi.getMaBuucuc()) && item.getLoaiChiTieu().equals(kpi.getLoaiChiTieu())) {
                    float tlHoanThanh =kpi.getChiTieuGiao() == 0 ? 0 :  item.getTlPhatDG() * 100 / kpi.getChiTieuGiao();
                    TopModel top = addTopModelToList(ngayBaocao, Const.PHAT_TC_DG, item, kpi, item.getTlPhatDG(), tlHoanThanh, 0);
                    topModelList.add(top);
                }
            }

            for (KPI kpi : kpiHoan) {
                if (item != null && item.getMaChiNhanh().equals(kpi.getMaChiNhanh()) && item.getMaBuuCuc().equals(kpi.getMaBuucuc()) && item.getLoaiChiTieu().equals(kpi.getLoaiChiTieu())) {
                    float tlHoanThanh = kpi.getChiTieuGiao() == 0 ? 0 : item.getTlHoan() * 100 / kpi.getChiTieuGiao();
                    TopModel top = addTopModelToList(ngayBaocao, Const.HOAN, item, kpi, item.getTlHoan(), tlHoanThanh, 0);
                    topModelList.add(top);
                }
            }
        }

        System.out.println("Khau phat - hoan : " + topModelList.size());

        System.out.println("Finish process khau phat - hoan ");

        //process leadtime
        System.out.println("Start process leadtime ");

        for (LeadTimeModel item : leadTimeAll) {
            for (KPI kpi : kpiNoiTinh) {
                if (item != null && item.getMaChiNhanh().equals(kpi.getMaChiNhanh()) && item.getMaBuuCuc().equals(kpi.getMaBuucuc()) && item.getLoaiChiTieu().equals(kpi.getLoaiChiTieu())) {
                    float tlHoanThanh = kpi.getChiTieuGiao() == 0 ? 0 : item.getTgNoiTinhAll() * 100 / kpi.getChiTieuGiao();
                    TopModel top = addTopModelToList(ngayBaocao, Const.NOI_TINH, item, kpi, 0, tlHoanThanh, item.getTgNoiTinhAll());
                    topModelList.add(top);
                }
            }
        }
        System.out.println("Khau lead-time : " + topModelList.size());
        System.out.println("Finish process leadtime ");

        return topModelList;
    }

    // Phương thức để thêm một TopModel vào danh sách
    private TopModel addTopModelToList(String ngayBaocao, String typeBaocao, KhauModel item, KPI kpi, float tlThucHien, float tlHoanThanh, float tgtt) {
        TopModel top = new TopModel();
        top.setNgayBaocao(ngayBaocao);
        top.setMaChiNhanh(item.getMaChiNhanh());
        top.setMaBuucuc(item.getMaBuuCuc());
        top.setType(typeBaocao);
        top.setIsLuyKe(item.getLuyKe());
        top.setTlThucHien(tlThucHien);
        top.setTlHoanThanh(tlHoanThanh);
        top.setKpi(kpi.getChiTieuGiao());
        top.setTgtt(tgtt);
        return top;
    }

    private List<KhauModel> khauPhatList(String ngayBaocao, String query, int luyke) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<KhauModel> khauPhatList = new ArrayList<>();
        try {
            //connection query
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();

            while (rs.next()) {
                KhauModel model = new KhauModel();
                model.setNgayBaoCao(ngayBaocao);
                model.setMaChiNhanh(rs.getString("MA_CN"));
                model.setMaBuuCuc(rs.getString("MA_BC"));
                model.setLuyKe(luyke);
                model.setSlPhatTCThucTe(rs.getLong("SL_PHAT_THANHCONG_THUCTE"));
                model.setSlPhatYC(rs.getLong("SL_PHAT_YEUCAU"));
                model.setSlPhatDungGio(rs.getLong("SL_PHAT_DUNGGIO"));
                model.setSlHoan(rs.getLong("SL_PHAT_HOAN"));
                model.setTlPhatTC(rs.getLong("TL_PHAT_THANHCONG"));
                model.setTlPhatDG(rs.getFloat("TL_PHAT_DUNGGIO"));
                model.setTlHoan(rs.getFloat("TL_PHAT_HOAN"));
                model.setLoaiChiTieu(rs.getString("LOAI_CHI_TIEU"));
                khauPhatList.add(model);
            }

        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return khauPhatList;
    }

    private List<KhauModel> khauThuTc(String ngayBaocao, String query, int luyke) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<KhauModel> khauThuTCList = new ArrayList<>();

        try {
            //connection query
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();

            while (rs.next()) {
                KhauModel model = new KhauModel();
                model.setNgayBaoCao(ngayBaocao);
                model.setMaChiNhanh(rs.getString("MA_CN"));
                model.setMaBuuCuc(rs.getString("MA_BC"));
                model.setLuyKe(luyke);
                model.setSlThuTC(rs.getLong("SL_THU_THANHCONG"));
                model.setSlThuYC(rs.getLong("SL_THU_YEUCAU"));
                model.setSlThuTCDungGio(rs.getLong("SL_THU_DUNGGIO"));
                model.setTlThuDG(rs.getFloat("TL_THU_DUNGGIO"));
                model.setTlThuTC(rs.getFloat("TL_THU_THANHCONG"));
                model.setLoaiChiTieu(rs.getString("LOAI_CHI_TIEU"));
                khauThuTCList.add(model);
            }


        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return khauThuTCList;
    }


    private List<KPI> getListKPI(Configuration config, String thang, String chitieu) {

        List<KPI> listKPI = new ArrayList<>();

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {
            String sqlQuery = queryKPI(thang, chitieu);

            try (PreparedStatement preparedStatement = connection.prepareStatement(sqlQuery)) {
                try (ResultSet resultSet = preparedStatement.executeQuery()) {
                    while (resultSet.next()) {
                        KPI kpi = new KPI();
                        kpi.setMaChiNhanh(resultSet.getString("ma_cn"));
                        kpi.setMaBuucuc(resultSet.getString("ma_bc"));
                        kpi.setThang(resultSet.getString("thang"));
                        kpi.setChiTieuGiao(resultSet.getFloat("chi_tieu_giao"));
                        kpi.setLoaiChiTieu(resultSet.getString("loai_chi_tieu"));
                        kpi.setChiTieu(resultSet.getString("chi_tieu"));
                        listKPI.add(kpi);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return listKPI;
    }

    private List<LeadTimeModel> getListTime(Configuration config, String ngayBaocao, String tblLeadTime, int isLuyKe) {

        List<LeadTimeModel> listKPI = new ArrayList<>();

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_INDEX"), config.getConfig("POSTGRES.USER_INDEX"), config.getConfig("POSTGRES.PASSWD_INDEX"))) {
            String sqlQuery = queryLeadTime(ngayBaocao, tblLeadTime);

            try (PreparedStatement preparedStatement = connection.prepareStatement(sqlQuery)) {
                try (ResultSet resultSet = preparedStatement.executeQuery()) {
                    while (resultSet.next()) {
                        LeadTimeModel kpi = new LeadTimeModel();
                        kpi.setNgayBaoCao(resultSet.getString("ngay_baocao"));
                        kpi.setMaChiNhanh(resultSet.getString("ma_chinhanh"));
                        kpi.setMaBuuCuc(resultSet.getString("ma_buucuc"));
                        kpi.setTgNoiTinhAll(resultSet.getFloat("tg_noitinh_all"));
                        kpi.setTgFmAll(resultSet.getFloat("tg_fm_all"));
                        kpi.setTgLmAll(resultSet.getFloat("tg_lm_all"));
                        kpi.setLoaiChiTieu(resultSet.getString("loai_chi_tieu"));
                        kpi.setLuyKe(isLuyKe);
                        listKPI.add(kpi);
                    }
                }
            }
        } catch (SQLException e) {
            System.out.println("SQLException : " + e.getMessage());
        }
        return listKPI;
    }

    private String queryKhauPhatCNBC(String ngayBacao, String table, String maChiSo) {
        String sql = "SELECT " +
                "MA_CN, " +
                "MA_BC, " +
                "'Bưu cục' as LOAI_CHI_TIEU," +
                "SUM(SL_PHAT_YEUCAU) AS SL_PHAT_YEUCAU, " +
                "SUM(SL_PHAT_THANHCONG_THUCTE) AS SL_PHAT_THANHCONG_THUCTE, " +
                "SUM(SL_PHAT_DUNGGIO) AS SL_PHAT_DUNGGIO, " +
                "SUM(SL_PHAT_THANHCONG_501) AS SL_PHAT_THANHCONG_501, " +
                "SUM(SL_PHAT_HOAN) AS SL_PHAT_HOAN, " +
                "(SUM(SL_PHAT_THANHCONG_THUCTE) * 100 / (CASE WHEN SUM(SL_PHAT_YEUCAU) <> 0 THEN SUM(SL_PHAT_YEUCAU) ELSE 1 END)) as TL_PHAT_THANHCONG, " +
                "(SUM(SL_PHAT_DUNGGIO) * 100 / (CASE WHEN SUM(SL_PHAT_THANHCONG_501) <> 0 THEN SUM(SL_PHAT_THANHCONG_501) ELSE 1 END)) as TL_PHAT_DUNGGIO, " +
                "(SUM(SL_PHAT_HOAN) * 100 / (CASE WHEN SUM(SL_PHAT_YEUCAU) <> 0 THEN SUM(SL_PHAT_YEUCAU) ELSE 1 END)) as TL_PHAT_HOAN " +
                "FROM %s WHERE version = %s and MA_CN not in ('CNTCT','FFM','LOG','ALL') and ma_vungcon != 'ALL' and NGAY_KPI = date '%s' group by MA_CN, MA_BC ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBacao), ngayBacao);
        return sql;
    }

    private String queryKhauPhatCN(String ngayBacao, String table, String maChiSo) {
        String sql = "SELECT " +
                "MA_CN, " +
                "'N/A' as MA_BC, " +
                "'Chi nhánh' as LOAI_CHI_TIEU," +
                "SUM(SL_PHAT_YEUCAU) AS SL_PHAT_YEUCAU, " +
                "SUM(SL_PHAT_THANHCONG_THUCTE) AS SL_PHAT_THANHCONG_THUCTE, " +
                "SUM(SL_PHAT_DUNGGIO) AS SL_PHAT_DUNGGIO, " +
                "SUM(SL_PHAT_THANHCONG_501) AS SL_PHAT_THANHCONG_501, " +
                "SUM(SL_PHAT_HOAN) AS SL_PHAT_HOAN, " +
                "(SUM(SL_PHAT_THANHCONG_THUCTE) * 100 / (CASE WHEN SUM(SL_PHAT_YEUCAU) <> 0 THEN SUM(SL_PHAT_YEUCAU) ELSE 1 END)) as TL_PHAT_THANHCONG, " +
                "(SUM(SL_PHAT_DUNGGIO) * 100 / (CASE WHEN SUM(SL_PHAT_THANHCONG_501) <> 0 THEN SUM(SL_PHAT_THANHCONG_501) ELSE 1 END)) as TL_PHAT_DUNGGIO, " +
                "(SUM(SL_PHAT_HOAN) * 100 / (CASE WHEN SUM(SL_PHAT_YEUCAU) <> 0 THEN SUM(SL_PHAT_YEUCAU) ELSE 1 END)) as TL_PHAT_HOAN " +
                "FROM %s WHERE version = %s and MA_CN not in ('CNTCT','FFM','LOG','ALL') and ma_vungcon != 'ALL' and NGAY_KPI = date '%s' group by MA_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBacao), ngayBacao);
        return sql;
    }


    private String queryKhauThuTCNgayCNBC(String ngaybaocao, String table, String maChiSo) {
        String sql = "SELECT " +
                "MA_CN, " +
                "MA_BC, " +
                "'Bưu cục' as LOAI_CHI_TIEU," +
                "SUM(SL_THU_YEUCAU) AS SL_THU_YEUCAU, " +
                "SUM(SL_THU_THANHCONG) AS SL_THU_THANHCONG, " +
                "SUM(SL_THU_DUNGGIO) AS SL_THU_DUNGGIO, " +
                "(SUM(SL_THU_THANHCONG) * 100 / (CASE WHEN SUM(SL_THU_YEUCAU) <> 0 THEN SUM(SL_THU_YEUCAU) ELSE 1 END)) as TL_THU_THANHCONG, " +
                "(SUM(SL_THU_DUNGGIO) * 100 / (CASE WHEN SUM(SL_THU_THANHCONG) <> 0 THEN SUM(SL_THU_THANHCONG) ELSE 1 END)) as TL_THU_DUNGGIO " +
                "FROM %s WHERE version = %s and MA_CN not in ('CNTCT','FFM','LOG','ALL') and ma_vung_con != 'ALL' and NGAY_KPI = date '%s' group by MA_CN, MA_BC ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngaybaocao), ngaybaocao);
        return sql;
    }

    private String queryKhauThuTCNgayCN(String ngaybaocao, String table, String maChiSo) {
        String sql = "SELECT " +
                "MA_CN, " +
                "'N/A' as MA_BC, " +
                "'Chi nhánh' as LOAI_CHI_TIEU," +
                "SUM(SL_THU_YEUCAU) AS SL_THU_YEUCAU, " +
                "SUM(SL_THU_THANHCONG) AS SL_THU_THANHCONG, " +
                "SUM(SL_THU_DUNGGIO) AS SL_THU_DUNGGIO, " +
                "(SUM(SL_THU_THANHCONG) * 100 / (CASE WHEN SUM(SL_THU_YEUCAU) <> 0 THEN SUM(SL_THU_YEUCAU) ELSE 1 END)) as TL_THU_THANHCONG, " +
                "(SUM(SL_THU_DUNGGIO) * 100 / (CASE WHEN SUM(SL_THU_THANHCONG) <> 0 THEN SUM(SL_THU_THANHCONG) ELSE 1 END)) as TL_THU_DUNGGIO " +
                "FROM %s WHERE version = %s and MA_CN not in ('CNTCT','FFM','LOG','ALL') and ma_vung_con != 'ALL' and NGAY_KPI = date '%s' group by MA_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngaybaocao), ngaybaocao);
        return sql;
    }


    private String queryKPI(String thangBc, String chiTieu) {
        String sql = "select " +
                "ma_cn," +
                "CASE WHEN loai_chi_tieu = 'Chi nhánh' THEN 'N/A' ELSE ma_bc END as ma_bc," +
                "thang," +
                "chi_tieu_giao," +
                "loai_chi_tieu," +
                "chi_tieu" +
                " from chitieu_cldv where thang = '%s' and chi_tieu = '%s'";
        sql = String.format(sql, thangBc, chiTieu);
        return sql;
    }


    private String queryLeadTime(String ngayBaocao, String tbl) {
        String sql = "SELECT " +
                "ngay_baocao," +
                "ma_chinhanh," +
                "ma_buucuc," +
                "tg_noitinh_all," +
                "tg_fm_all," +
                "tg_lm_all," +
                "'Bưu cục' as  loai_chi_tieu" +
                " FROM %s WHERE ngay_baocao = '%s' and ma_chinhanh not in ('CNTCT','FFM','LOG')" +
                " UNION " +
                " SELECT " +
                "'%s' ngay_baocao," +
                "ma_chinhanh," +
                "'N/A' as ma_buucuc," +
                "avg(tg_noitinh_all) as tg_noitinh_all," +
                "avg(tg_fm_all) as tg_fm_all," +
                "avg(tg_lm_all) as tg_lm_all," +
                "'Chi nhánh' as  loai_chi_tieu" +
                " FROM %s WHERE ngay_baocao = '%s' and ma_chinhanh not in ('CNTCT','FFM','LOG') group by ma_chinhanh";
        sql = String.format(sql, tbl, ngayBaocao, ngayBaocao, tbl, ngayBaocao);
        return sql;
    }
}
