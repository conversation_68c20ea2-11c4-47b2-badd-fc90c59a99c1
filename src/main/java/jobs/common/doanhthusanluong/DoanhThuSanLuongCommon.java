package jobs.common.doanhthusanluong;

import connection.PhoenixConnectionFactory;
import model.doanhthusanluong.DoanhThuSanLuong;
import utils.AbstractDao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DoanhThuSanLuongCommon  extends AbstractDao {

    private final String tableDoanhThu = "DASH_DIEUHANH_DOANHTHU";
    private final String maChiSo = "dash_dieuhanh_doanhthu";

    /** <PERSON>i<PERSON>u đồ cần hiển thị doanh thu & san lượng của 12 tháng gần nhất, t<PERSON>h cả tháng hiện tại
     * Do đó việc job này cần làm là:
     * 1. Tìm list 11 ngày cuối tháng (ngay_baocao + version) trong quá khứ
     * 2. query ra 11 bản ghi với list trên để tính từng loại doanh thu/sản lương **/

    public List<DoanhThuSanLuong> calculateData(String ngayBaoCao) {
        List<DoanhThuSanLuong> reponseList = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate currentDate = LocalDate.parse(ngayBaoCao, formatter);
        Map<LocalDate, Long> map = getListVersionsFromListDays(currentDate);

        for (Map.Entry<LocalDate, Long> entry : map.entrySet()) {
            LocalDate key = entry.getKey();
            Long value = entry.getValue();
            System.out.println("Key: " + key + ", Value: " + value);
            List<DoanhThuSanLuong> doanhThuSanLuongBC = getDoanhThuSanLuongBC(key.toString(), queryDoanhThuSanLuongBC(key.toString(), tableDoanhThu, value));
            reponseList.addAll(doanhThuSanLuongBC);
            List<DoanhThuSanLuong> doanhThuSanLuongCN = getDoanhThuSanLuongCN(key.toString(), queryDoanhThuSanLuongCN(key.toString(), tableDoanhThu, value));
            reponseList.addAll(doanhThuSanLuongCN);
            List<DoanhThuSanLuong> doanhThuSanLuongAllCN = getDoanhThuSanLuongAllCN(key.toString(), queryDoanhThuSanLuongAllCN(key.toString(), tableDoanhThu, value));
            reponseList.addAll(doanhThuSanLuongAllCN);
        }
        return reponseList;
    }



    private Map<LocalDate, Long> getListVersionsFromListDays(LocalDate currentDate) {
        List<LocalDate> dates = getLast11DaysOfPreviousMonths(currentDate);
        Map<LocalDate, Long> response = new HashMap<>();
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlDateCondition = "";
        if (dates != null && dates.size() > 0 && !dates.get(0).equals("!@#$%")) {
            sqlDateCondition += String.format("(MA_CHISO = '%s' AND NGAY_BAOCAO = DATE '%s')", maChiSo, currentDate);
            dates.remove(currentDate);
            for (LocalDate x : dates) {
                sqlDateCondition += String.format(" OR (MA_CHISO = '%s' AND NGAY_BAOCAO = DATE '%s') ", maChiSo, x);
            }
        }

        String sqlMax = "SELECT NGAY_BAOCAO, MAX(VERSION) AS VERSION FROM CHISO_VERSION WHERE " + sqlDateCondition + " GROUP BY NGAY_BAOCAO";
        LocalDate ngayBaoCao;
        Long maxVersion;
        System.out.println("sql------------" + sqlMax);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            while (rs.next()) {
                ngayBaoCao = rs.getDate("NGAY_BAOCAO").toLocalDate();
                maxVersion = rs.getLong("version");
                response.put(ngayBaoCao, maxVersion);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}" + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private List<LocalDate> getLast11DaysOfPreviousMonths(LocalDate currentDate) {
        List<LocalDate> dates = new ArrayList<>();
        // Bắt đầu từ tháng trước tháng hiện tại
        LocalDate date = currentDate.minusMonths(1);

        // Lấy ngày cuối của 11 tháng trước đó
        for (int i = 1; i <= date.getMonthValue(); i++) {
            YearMonth yearMonth = YearMonth.of(date.getYear(), i);
            LocalDate endOfMonth = yearMonth.atEndOfMonth();
            dates.add(endOfMonth);
            System.out.println(endOfMonth);
            // Chuyển sang tháng trước đó
//            date = date.minusMonths(1);
        }
        return dates;
    }

    private String queryDoanhThuSanLuongBC(String ngayBaoCao, String table, Long version) {
        String sql = "SELECT NGAY_BAOCAO, MA_CN, MA_BUUCUC, " +

                "SUM(CASE WHEN NHOM_DV = 'CPTN' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_CPTN, " +
                "SUM(CASE WHEN NHOM_DV = 'VT' or NHOM_DV = 'FWD' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_VT, " +
                "SUM(CASE WHEN NHOM_DV = 'EXP' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_EXP, " +
                "SUM(CASE WHEN NHOM_DV = 'KHO' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_KHO, " +
                "SUM(DT_LK_THANG) AS TONG_DT_LK_THANG, " +

                "SUM(CASE WHEN NHOM_DV = 'CPTN' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_CPTN, " +
                "SUM(CASE WHEN NHOM_DV = 'VT' or NHOM_DV = 'FWD' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_VT, " +
                "SUM(CASE WHEN NHOM_DV = 'EXP' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_EXP, " +
                "SUM(CASE WHEN NHOM_DV = 'KHO' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_KHO, " +
                "SUM(SL_LK_CHUYENPHAT_THANG) AS TONG_SL_LK_THANG, " +

                "UPDATED_AT " +
                "FROM %s " +
                "WHERE VERSION = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY NGAY_BAOCAO, MA_CN, MA_BUUCUC, UPDATED_AT";
        sql = String.format(sql, table, version, ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<DoanhThuSanLuong> getDoanhThuSanLuongBC(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<DoanhThuSanLuong> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                DoanhThuSanLuong data = new DoanhThuSanLuong(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getDouble("TONG_DT_LK_THANG"),
                        rs.getDouble("DT_CPTN"),
                        rs.getDouble("DT_VT"),
                        rs.getDouble("DT_EXP"),
                        rs.getDouble("DT_KHO"),
                        rs.getLong("TONG_SL_LK_THANG"),
                        rs.getLong("SL_CPTN"),
                        rs.getLong("SL_VT"),
                        rs.getLong("SL_EXP"),
                        rs.getLong("SL_KHO"),
                        rs.getLong("updated_at")
                        );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuSanLuongCN(String ngayBaoCao, String table, Long version) {
        String sql = "SELECT NGAY_BAOCAO, MA_CN, " +

                "SUM(CASE WHEN NHOM_DV = 'CPTN' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_CPTN, " +
                "SUM(CASE WHEN NHOM_DV = 'VT' or NHOM_DV = 'FWD' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_VT, " +
                "SUM(CASE WHEN NHOM_DV = 'EXP' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_EXP, " +
                "SUM(CASE WHEN NHOM_DV = 'KHO' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_KHO, " +
                "SUM(DT_LK_THANG) AS TONG_DT_LK_THANG, " +

                "SUM(CASE WHEN NHOM_DV = 'CPTN' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_CPTN, " +
                "SUM(CASE WHEN NHOM_DV = 'VT' or NHOM_DV = 'FWD' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_VT, " +
                "SUM(CASE WHEN NHOM_DV = 'EXP' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_EXP, " +
                "SUM(CASE WHEN NHOM_DV = 'KHO' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_KHO, " +
                "SUM(SL_LK_CHUYENPHAT_THANG) AS TONG_SL_LK_THANG, " +

                "UPDATED_AT " +
                "FROM %s " +
                "WHERE VERSION = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY NGAY_BAOCAO, MA_CN, UPDATED_AT";
        sql = String.format(sql, table, version, ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<DoanhThuSanLuong> getDoanhThuSanLuongCN(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<DoanhThuSanLuong> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                DoanhThuSanLuong data = new DoanhThuSanLuong(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getDouble("TONG_DT_LK_THANG"),
                        rs.getDouble("DT_CPTN"),
                        rs.getDouble("DT_VT"),
                        rs.getDouble("DT_EXP"),
                        rs.getDouble("DT_KHO"),
                        rs.getLong("TONG_SL_LK_THANG"),
                        rs.getLong("SL_CPTN"),
                        rs.getLong("SL_VT"),
                        rs.getLong("SL_EXP"),
                        rs.getLong("SL_KHO"),
                        rs.getLong("updated_at")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuSanLuongAllCN(String ngayBaoCao, String table, Long version) {
        String sql = "SELECT NGAY_BAOCAO, " +

                "SUM(CASE WHEN NHOM_DV = 'CPTN' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_CPTN, " +
                "SUM(CASE WHEN NHOM_DV = 'VT' or NHOM_DV = 'FWD' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_VT, " +
                "SUM(CASE WHEN NHOM_DV = 'EXP' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_EXP, " +
                "SUM(CASE WHEN NHOM_DV = 'KHO' " +
                "THEN DT_LK_THANG " +
                "ELSE 0 " +
                "END) AS DT_KHO, " +
                "SUM(DT_LK_THANG) AS TONG_DT_LK_THANG, " +

                "SUM(CASE WHEN NHOM_DV = 'CPTN' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_CPTN, " +
                "SUM(CASE WHEN NHOM_DV = 'VT' or NHOM_DV = 'FWD' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_VT, " +
                "SUM(CASE WHEN NHOM_DV = 'EXP' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_EXP, " +
                "SUM(CASE WHEN NHOM_DV = 'KHO' " +
                "THEN SL_LK_CHUYENPHAT_THANG " +
                "ELSE 0 " +
                "END) AS SL_KHO, " +
                "SUM(SL_LK_CHUYENPHAT_THANG) AS TONG_SL_LK_THANG, " +

                "UPDATED_AT " +
                "FROM %s " +
                "WHERE VERSION = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY NGAY_BAOCAO, UPDATED_AT";
        sql = String.format(sql, table, version, ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<DoanhThuSanLuong> getDoanhThuSanLuongAllCN(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<DoanhThuSanLuong> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                DoanhThuSanLuong data = new DoanhThuSanLuong(
                        ngayBaoCao,
                        "N/A",
                        "N/A",
                        rs.getDouble("TONG_DT_LK_THANG"),
                        rs.getDouble("DT_CPTN"),
                        rs.getDouble("DT_VT"),
                        rs.getDouble("DT_EXP"),
                        rs.getDouble("DT_KHO"),
                        rs.getLong("TONG_SL_LK_THANG"),
                        rs.getLong("SL_CPTN"),
                        rs.getLong("SL_VT"),
                        rs.getLong("SL_EXP"),
                        rs.getLong("SL_KHO"),
                        rs.getLong("updated_at")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }
}
