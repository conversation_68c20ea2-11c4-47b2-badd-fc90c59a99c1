package jobs.common.smsdoanhthu;

import configs.Configuration;
import connection.PhoenixConnectionFactory;
import constant.Const;
import model.smsdoanhthu.SMSDoanhThu;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import utils.AbstractDao;

import java.io.IOException;
import java.io.InputStream;
import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SMSDoanhThuCommon extends AbstractDao {

    private final String tableDoanhThu = "DASH_DIEUHANH_DOANHTHU";
    private final String tableChiTieuDoanhThu = "chitieu_doanhthu";
    private final String maChiSo = "dash_dieuhanh_doanhthu";

    //send data of n-1 date
    //lanh dao TCT: all data of 63 branches, FFM, LOG
    // branch director: data of this branch
    public List<SMSDoanhThu> calculateData(Configuration config, String ngayBaocao) throws IOException {
        // BUU CUC RECORDS
        List<SMSDoanhThu> sanLuongBCList = getSanLuongBCDataFromQuery(ngayBaocao, querySanLuongBC(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuBCList = getDoanhThuBCDataFromQuery(ngayBaocao, queryDoanhThuBC(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuBCCungKyList = getDoanhThuBCCungKyDataFromQuery(ngayBaocao, queryDoanhThuBCCungKy(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuCPBCList = getDoanhThuCPBCDataFromQuery(ngayBaocao, queryDoanhThuCPBC(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuVTKhoBCList = getDoanhThuVTKhoBCDataFromQuery(ngayBaocao, queryDoanhThuVTKhoBC(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuTMUTBCList = getDoanhThuTMUTBCDataFromQuery(ngayBaocao, queryDoanhThuTMUTBC(ngayBaocao, tableDoanhThu, maChiSo));

        // CHI NHANH RECORDS
        List<SMSDoanhThu> sanLuongCNList = getSanLuongCNDataFromQuery(ngayBaocao, querySanLuongCN(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuCNList = getDoanhThuCNDataFromQuery(ngayBaocao, queryDoanhThuCN(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuCNCungKyList = getDoanhThuCNCungKyDataFromQuery(ngayBaocao, queryDoanhThuCNCungKy(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuCPCNList = getDoanhThuCPCNDataFromQuery(ngayBaocao, queryDoanhThuCPCN(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuVTKhoCNList = getDoanhThuVTKhoCNDataFromQuery(ngayBaocao, queryDoanhThuVTKhoCN(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuTMUTCNList = getDoanhThuTMUTCNDataFromQuery(ngayBaocao, queryDoanhThuTMUTCN(ngayBaocao, tableDoanhThu, maChiSo));

        // ALL RECORD
        List<Double> dtKeHoach =  getDoanhThuKeHoach(ngayBaocao, tableDoanhThu, maChiSo);
        List<SMSDoanhThu> sanLuongAllList = getSanLuongAllDataFromQuery(ngayBaocao, querySanLuongAll(ngayBaocao, tableDoanhThu, maChiSo));
        List<SMSDoanhThu> doanhThuAllList = getDoanhThuAllDataFromQuery(ngayBaocao, queryDoanhThuAll(ngayBaocao, tableDoanhThu, maChiSo), dtKeHoach);
        List<SMSDoanhThu> doanhThuAllCungKyList = getDoanhThuAllCungKyDataFromQuery(ngayBaocao, queryDoanhThuAllCungKy(ngayBaocao, tableDoanhThu, maChiSo));
        List<Double> dtKeHoachCP =  getDoanhThuKeHoachCP(ngayBaocao, tableDoanhThu, maChiSo);
        List<SMSDoanhThu> doanhThuCPAllList = getDoanhThuCPAllDataFromQuery(ngayBaocao, queryDoanhThuCPAll(ngayBaocao, tableDoanhThu, maChiSo), dtKeHoachCP);
        List<Double> dtKeHoachVTKho =  getDoanhThuKeHoachVTKho(ngayBaocao, tableDoanhThu, maChiSo);
        List<SMSDoanhThu> doanhThuVTKhoAllList = getDoanhThuVTKhoAllDataFromQuery(ngayBaocao, queryDoanhThuVTKhoAll(ngayBaocao, tableDoanhThu, maChiSo), dtKeHoachVTKho);
        List<Double> dtKeHoachTMUT =  getDoanhThuKeHoachTMUT(config, ngayBaocao, tableChiTieuDoanhThu);
        List<SMSDoanhThu> doanhThuTMUTAllList = getDoanhThuTMUTAllDataFromQuery(ngayBaocao, queryDoanhThuTMUTAll(ngayBaocao, tableDoanhThu, maChiSo), dtKeHoachTMUT);

        List<SMSDoanhThu> finalResult = new ArrayList<>();

        /** Merge buu cuc's records **/
        for (SMSDoanhThu sanLuong : sanLuongBCList) {
            SMSDoanhThu object = new SMSDoanhThu();
            for (SMSDoanhThu doanhThu : doanhThuBCList) {
                if (!Objects.isNull(sanLuong) && !Objects.isNull(doanhThu) &&
                sanLuong.getMaChiNhanh().equals(doanhThu.getMaChiNhanh()) && sanLuong.getMaBuuCuc().equals(doanhThu.getMaBuuCuc())) {
                    object.setNgayBaoCao(sanLuong.getNgayBaoCao());
                    object.setMaChiNhanh(sanLuong.getMaChiNhanh());
                    object.setMaBuuCuc(sanLuong.getMaBuuCuc());
                    object.setSlNgay(sanLuong.getSlNgay());
                    object.setSlLKThang(sanLuong.getSlLKThang());
                    object.setSlCKThang(sanLuong.getSlCKThang());
                    object.setSlTTCKThang(sanLuong.getSlTTCKThang());
                    object.setSlTTTBNThang(sanLuong.getSlTTTBNThang());
                    object.setSlCKNam(sanLuong.getSlCKNam());
                    object.setSlTTCKNam(sanLuong.getSlTTCKNam());
                    object.setSlTTTBNNam(sanLuong.getSlTTTBNNam());

                    object.setDtNgay(doanhThu.getDtNgay());
                    object.setDtLKThang(doanhThu.getDtLKThang());
                    object.setDtKeHoach(doanhThu.getDtKeHoach());
                    object.setTiLeHoanThanh(doanhThu.getTiLeHoanThanh());
                    object.setTienDoDT(doanhThu.getTienDoDT());
                }
            }

            for (SMSDoanhThu doanhThuCungKy : doanhThuBCCungKyList) {
                if (!Objects.isNull(sanLuong) && !Objects.isNull(doanhThuCungKy) &&
                        sanLuong.getMaChiNhanh().equals(doanhThuCungKy.getMaChiNhanh()) && sanLuong.getMaBuuCuc().equals(doanhThuCungKy.getMaBuuCuc())) {
                    object.setDtCKThang(doanhThuCungKy.getDtCKThang());
                    object.setDtTTCKThang(doanhThuCungKy.getDtTTCKThang());
                    object.setDtCKNam(doanhThuCungKy.getDtCKNam());
                    object.setDtTTCKNam(doanhThuCungKy.getDtTTCKNam());
                }
            }

            for (SMSDoanhThu doanhThuCP : doanhThuCPBCList) {
                if (!Objects.isNull(sanLuong) && !Objects.isNull(doanhThuCP) &&
                        sanLuong.getMaChiNhanh().equals(doanhThuCP.getMaChiNhanh()) && sanLuong.getMaBuuCuc().equals(doanhThuCP.getMaBuuCuc())) {
                    object.setDtNgayCP(doanhThuCP.getDtNgayCP());
                    object.setDtLKThangCP(doanhThuCP.getDtLKThangCP());
                    object.setDtKeHoachCP(doanhThuCP.getDtKeHoachCP());
                    object.setTiLeHoanThanhKHCP(doanhThuCP.getTiLeHoanThanhKHCP());
                    object.setTienDoDTCP(doanhThuCP.getTienDoDTCP());
                    object.setDtCKThangCP(doanhThuCP.getDtCKThangCP());
                    object.setDtTTCKThangCP(doanhThuCP.getDtTTCKThangCP());
                    object.setDtTTTBNCKThangCP(doanhThuCP.getDtTTTBNCKThangCP());
                    object.setDtCKNamCP(doanhThuCP.getDtCKNamCP());
                    object.setDtTTCKNamCP(doanhThuCP.getDtTTCKNamCP());
                    object.setDtTTTBNCKNamCP(doanhThuCP.getDtTTTBNCKNamCP());
                }
            }

            for (SMSDoanhThu doanhThuVTKho : doanhThuVTKhoBCList) {
                if (!Objects.isNull(sanLuong) && !Objects.isNull(doanhThuVTKho) &&
                        sanLuong.getMaChiNhanh().equals(doanhThuVTKho.getMaChiNhanh()) && sanLuong.getMaBuuCuc().equals(doanhThuVTKho.getMaBuuCuc())) {
                    object.setDtNgayVTKho(doanhThuVTKho.getDtNgayVTKho());
                    object.setDtLKThangVTKho(doanhThuVTKho.getDtLKThangVTKho());
                    object.setDtKeHoachVTKho(doanhThuVTKho.getDtKeHoachVTKho());
                    object.setTlhtKeHoachVTKho(doanhThuVTKho.getTlhtKeHoachVTKho());
                    object.setTienDoDTVTKho(doanhThuVTKho.getTienDoDTVTKho());
                    object.setDtCKThangVTKho(doanhThuVTKho.getDtCKThangVTKho());
                    object.setDtTTCKThangVTKho(doanhThuVTKho.getDtTTCKThangVTKho());
                    object.setDtCKNamVTKho(doanhThuVTKho.getDtCKNamVTKho());
                    object.setDtTTCKNamVTKho(doanhThuVTKho.getDtTTCKNamVTKho());
                }
            }

            for (SMSDoanhThu doanhThuTMUT : doanhThuTMUTBCList) {
                if (!Objects.isNull(sanLuong) && !Objects.isNull(doanhThuTMUT) &&
                        sanLuong.getMaChiNhanh().equals(doanhThuTMUT.getMaChiNhanh()) && sanLuong.getMaBuuCuc().equals(doanhThuTMUT.getMaBuuCuc())) {
                    object.setDtNgayTMUT(doanhThuTMUT.getDtNgayTMUT());
                    object.setDtLKThangTMUT(doanhThuTMUT.getDtLKThangTMUT());
                    object.setDtKeHoachTMUT(doanhThuTMUT.getDtKeHoachTMUT());
                    object.setTlhtKeHoachTMUT(doanhThuTMUT.getTlhtKeHoachTMUT());
                    object.setTienDoDTTMUT(doanhThuTMUT.getTienDoDTTMUT());
                }
            }
            finalResult.add(object);
        }


        /** Merge chi nhanh's records **/
        for (SMSDoanhThu sanLuongCN : sanLuongCNList) {
            SMSDoanhThu objectCN = new SMSDoanhThu();
            for (SMSDoanhThu doanhThuCN : doanhThuCNList) {
                if (!Objects.isNull(sanLuongCN) && !Objects.isNull(doanhThuCN) &&
                        sanLuongCN.getMaChiNhanh().equals(doanhThuCN.getMaChiNhanh()) && sanLuongCN.getMaBuuCuc().equals(doanhThuCN.getMaBuuCuc())) {
                    objectCN.setNgayBaoCao(sanLuongCN.getNgayBaoCao());
                    objectCN.setMaChiNhanh(sanLuongCN.getMaChiNhanh());
                    objectCN.setMaBuuCuc(sanLuongCN.getMaBuuCuc());
                    objectCN.setSlNgay(sanLuongCN.getSlNgay());
                    objectCN.setSlLKThang(sanLuongCN.getSlLKThang());
                    objectCN.setSlCKThang(sanLuongCN.getSlCKThang());
                    objectCN.setSlTTCKThang(sanLuongCN.getSlTTCKThang());
                    objectCN.setSlTTTBNThang(sanLuongCN.getSlTTTBNThang());
                    objectCN.setSlCKNam(sanLuongCN.getSlCKNam());
                    objectCN.setSlTTCKNam(sanLuongCN.getSlTTCKNam());
                    objectCN.setSlTTTBNNam(sanLuongCN.getSlTTTBNNam());

                    objectCN.setDtNgay(doanhThuCN.getDtNgay());
                    objectCN.setDtLKThang(doanhThuCN.getDtLKThang());
                    objectCN.setDtKeHoach(doanhThuCN.getDtKeHoach());
                    objectCN.setTiLeHoanThanh(doanhThuCN.getTiLeHoanThanh());
                    objectCN.setTienDoDT(doanhThuCN.getTienDoDT());
                }
            }

            for (SMSDoanhThu doanhThuCNCungKy : doanhThuCNCungKyList) {
                if (!Objects.isNull(sanLuongCN) && !Objects.isNull(doanhThuCNCungKy) &&
                        sanLuongCN.getMaChiNhanh().equals(doanhThuCNCungKy.getMaChiNhanh()) && sanLuongCN.getMaBuuCuc().equals(doanhThuCNCungKy.getMaBuuCuc())) {
                    objectCN.setDtCKThang(doanhThuCNCungKy.getDtCKThang());
                    objectCN.setDtTTCKThang(doanhThuCNCungKy.getDtTTCKThang());
                    objectCN.setDtCKNam(doanhThuCNCungKy.getDtCKNam());
                    objectCN.setDtTTCKNam(doanhThuCNCungKy.getDtTTCKNam());
                }
            }

            for (SMSDoanhThu doanhThuCPCN : doanhThuCPCNList) {
                if (!Objects.isNull(sanLuongCN) && !Objects.isNull(doanhThuCPCN) &&
                        sanLuongCN.getMaChiNhanh().equals(doanhThuCPCN.getMaChiNhanh()) && sanLuongCN.getMaBuuCuc().equals(doanhThuCPCN.getMaBuuCuc())) {
                    objectCN.setDtNgayCP(doanhThuCPCN.getDtNgayCP());
                    objectCN.setDtLKThangCP(doanhThuCPCN.getDtLKThangCP());
                    objectCN.setDtKeHoachCP(doanhThuCPCN.getDtKeHoachCP());
                    objectCN.setTiLeHoanThanhKHCP(doanhThuCPCN.getTiLeHoanThanhKHCP());
                    objectCN.setTienDoDTCP(doanhThuCPCN.getTienDoDTCP());
                    objectCN.setDtCKThangCP(doanhThuCPCN.getDtCKThangCP());
                    objectCN.setDtTTCKThangCP(doanhThuCPCN.getDtTTCKThangCP());
                    objectCN.setDtTTTBNCKThangCP(doanhThuCPCN.getDtTTTBNCKThangCP());
                    objectCN.setDtCKNamCP(doanhThuCPCN.getDtCKNamCP());
                    objectCN.setDtTTCKNamCP(doanhThuCPCN.getDtTTCKNamCP());
                    objectCN.setDtTTTBNCKNamCP(doanhThuCPCN.getDtTTTBNCKNamCP());
                }
            }

            for (SMSDoanhThu doanhThuVTKhoCN : doanhThuVTKhoCNList) {
                if (!Objects.isNull(sanLuongCN) && !Objects.isNull(doanhThuVTKhoCN) &&
                        sanLuongCN.getMaChiNhanh().equals(doanhThuVTKhoCN.getMaChiNhanh()) && sanLuongCN.getMaBuuCuc().equals(doanhThuVTKhoCN.getMaBuuCuc())) {
                    objectCN.setDtNgayVTKho(doanhThuVTKhoCN.getDtNgayVTKho());
                    objectCN.setDtLKThangVTKho(doanhThuVTKhoCN.getDtLKThangVTKho());
                    objectCN.setDtKeHoachVTKho(doanhThuVTKhoCN.getDtKeHoachVTKho());
                    objectCN.setTlhtKeHoachVTKho(doanhThuVTKhoCN.getTlhtKeHoachVTKho());
                    objectCN.setTienDoDTVTKho(doanhThuVTKhoCN.getTienDoDTVTKho());
                    objectCN.setDtCKThangVTKho(doanhThuVTKhoCN.getDtCKThangVTKho());
                    objectCN.setDtTTCKThangVTKho(doanhThuVTKhoCN.getDtTTCKThangVTKho());
                    objectCN.setDtCKNamVTKho(doanhThuVTKhoCN.getDtCKNamVTKho());
                    objectCN.setDtTTCKNamVTKho(doanhThuVTKhoCN.getDtTTCKNamVTKho());
                }
            }

            for (SMSDoanhThu doanhThuTMUTCN : doanhThuTMUTBCList) {
                if (!Objects.isNull(sanLuongCN) && !Objects.isNull(doanhThuTMUTCN) &&
                        sanLuongCN.getMaChiNhanh().equals(doanhThuTMUTCN.getMaChiNhanh()) && sanLuongCN.getMaBuuCuc().equals(doanhThuTMUTCN.getMaBuuCuc())) {
                    objectCN.setDtNgayTMUT(doanhThuTMUTCN.getDtNgayTMUT());
                    objectCN.setDtLKThangTMUT(doanhThuTMUTCN.getDtLKThangTMUT());
                    objectCN.setDtKeHoachTMUT(doanhThuTMUTCN.getDtKeHoachTMUT());
                    objectCN.setTlhtKeHoachTMUT(doanhThuTMUTCN.getTlhtKeHoachTMUT());
                    objectCN.setTienDoDTTMUT(doanhThuTMUTCN.getTienDoDTTMUT());
                }
            }
            finalResult.add(objectCN);
        }

        /** Merge ALL (doanh thu all chi nhanh) records **/
        for (SMSDoanhThu sanLuongAll : sanLuongAllList) {
            SMSDoanhThu objectAll = new SMSDoanhThu();
            for (SMSDoanhThu doanhThuAll : doanhThuAllList) {
                if (!Objects.isNull(sanLuongAll) && !Objects.isNull(doanhThuAll) &&
                        sanLuongAll.getMaChiNhanh().equals(doanhThuAll.getMaChiNhanh()) && sanLuongAll.getMaBuuCuc().equals(doanhThuAll.getMaBuuCuc())) {
                    objectAll.setNgayBaoCao(sanLuongAll.getNgayBaoCao());
                    objectAll.setMaChiNhanh(sanLuongAll.getMaChiNhanh());
                    objectAll.setMaBuuCuc(sanLuongAll.getMaBuuCuc());
                    objectAll.setSlNgay(sanLuongAll.getSlNgay());
                    objectAll.setSlLKThang(sanLuongAll.getSlLKThang());
                    objectAll.setSlCKThang(sanLuongAll.getSlCKThang());
                    objectAll.setSlTTCKThang(sanLuongAll.getSlTTCKThang());
                    objectAll.setSlTTTBNThang(sanLuongAll.getSlTTTBNThang());
                    objectAll.setSlCKNam(sanLuongAll.getSlCKNam());
                    objectAll.setSlTTCKNam(sanLuongAll.getSlTTCKNam());
                    objectAll.setSlTTTBNNam(sanLuongAll.getSlTTTBNNam());

                    objectAll.setDtNgay(doanhThuAll.getDtNgay());
                    objectAll.setDtLKThang(doanhThuAll.getDtLKThang());
                    objectAll.setDtKeHoach(doanhThuAll.getDtKeHoach());
                    objectAll.setTiLeHoanThanh(doanhThuAll.getTiLeHoanThanh());
                    objectAll.setTienDoDT(doanhThuAll.getTienDoDT());
                }
            }

            for (SMSDoanhThu doanhThuAllCungKy : doanhThuAllCungKyList) {
                if (!Objects.isNull(sanLuongAll) && !Objects.isNull(doanhThuAllCungKy) &&
                        sanLuongAll.getMaChiNhanh().equals(doanhThuAllCungKy.getMaChiNhanh()) && sanLuongAll.getMaBuuCuc().equals(doanhThuAllCungKy.getMaBuuCuc())) {
                    objectAll.setDtCKThang(doanhThuAllCungKy.getDtCKThang());
                    objectAll.setDtTTCKThang(doanhThuAllCungKy.getDtTTCKThang());
                    objectAll.setDtCKNam(doanhThuAllCungKy.getDtCKNam());
                    objectAll.setDtTTCKNam(doanhThuAllCungKy.getDtTTCKNam());
                }
            }

            for (SMSDoanhThu doanhThuCPAll : doanhThuCPAllList) {
                if (!Objects.isNull(sanLuongAll) && !Objects.isNull(doanhThuCPAll) &&
                        sanLuongAll.getMaChiNhanh().equals(doanhThuCPAll.getMaChiNhanh()) && sanLuongAll.getMaBuuCuc().equals(doanhThuCPAll.getMaBuuCuc())) {
                    objectAll.setDtNgayCP(doanhThuCPAll.getDtNgayCP());
                    objectAll.setDtLKThangCP(doanhThuCPAll.getDtLKThangCP());
                    objectAll.setDtKeHoachCP(doanhThuCPAll.getDtKeHoachCP());
                    objectAll.setTiLeHoanThanhKHCP(doanhThuCPAll.getTiLeHoanThanhKHCP());
                    objectAll.setTienDoDTCP(doanhThuCPAll.getTienDoDTCP());
                    objectAll.setDtCKThangCP(doanhThuCPAll.getDtCKThangCP());
                    objectAll.setDtTTCKThangCP(doanhThuCPAll.getDtTTCKThangCP());
                    objectAll.setDtTTTBNCKThangCP(doanhThuCPAll.getDtTTTBNCKThangCP());
                    objectAll.setDtCKNamCP(doanhThuCPAll.getDtCKNamCP());
                    objectAll.setDtTTCKNamCP(doanhThuCPAll.getDtTTCKNamCP());
                    objectAll.setDtTTTBNCKNamCP(doanhThuCPAll.getDtTTTBNCKNamCP());
                }
            }

            for (SMSDoanhThu doanhThuVTKhoAll : doanhThuVTKhoAllList) {
                if (!Objects.isNull(sanLuongAll) && !Objects.isNull(doanhThuVTKhoAll) &&
                        sanLuongAll.getMaChiNhanh().equals(doanhThuVTKhoAll.getMaChiNhanh()) && sanLuongAll.getMaBuuCuc().equals(doanhThuVTKhoAll.getMaBuuCuc())) {
                    objectAll.setDtNgayVTKho(doanhThuVTKhoAll.getDtNgayVTKho());
                    objectAll.setDtLKThangVTKho(doanhThuVTKhoAll.getDtLKThangVTKho());
                    objectAll.setDtKeHoachVTKho(doanhThuVTKhoAll.getDtKeHoachVTKho());
                    objectAll.setTlhtKeHoachVTKho(doanhThuVTKhoAll.getTlhtKeHoachVTKho());
                    objectAll.setTienDoDTVTKho(doanhThuVTKhoAll.getTienDoDTVTKho());
                    objectAll.setDtCKThangVTKho(doanhThuVTKhoAll.getDtCKThangVTKho());
                    objectAll.setDtTTCKThangVTKho(doanhThuVTKhoAll.getDtTTCKThangVTKho());
                    objectAll.setDtCKNamVTKho(doanhThuVTKhoAll.getDtCKNamVTKho());
                    objectAll.setDtTTCKNamVTKho(doanhThuVTKhoAll.getDtTTCKNamVTKho());
                }
            }

            for (SMSDoanhThu doanhThuTMUTAll : doanhThuTMUTAllList) {
                if (!Objects.isNull(sanLuongAll) && !Objects.isNull(doanhThuTMUTAll) &&
                        sanLuongAll.getMaChiNhanh().equals(doanhThuTMUTAll.getMaChiNhanh()) && sanLuongAll.getMaBuuCuc().equals(doanhThuTMUTAll.getMaBuuCuc())) {
                    objectAll.setDtNgayTMUT(doanhThuTMUTAll.getDtNgayTMUT());
                    objectAll.setDtLKThangTMUT(doanhThuTMUTAll.getDtLKThangTMUT());
                    objectAll.setDtKeHoachTMUT(doanhThuTMUTAll.getDtKeHoachTMUT());
                    objectAll.setTlhtKeHoachTMUT(doanhThuTMUTAll.getTlhtKeHoachTMUT());
                    objectAll.setTienDoDTTMUT(doanhThuTMUTAll.getTienDoDTTMUT());
                }
            }
            finalResult.add(objectAll);
        }
        return finalResult;
    }

    private String querySanLuongBC(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, MA_BUUCUC, " +
                "SUM(SL_CHUYENPHAT_NGAY) AS SL_CHUYENPHAT_NGAY," +
                "SUM(SL_LK_CHUYENPHAT_THANG) AS SL_LK_CHUYENPHAT_THANG, " +
                "SUM(SL_LK_CHUYENPHAT_THANGTRUOC) AS SL_LK_CHUYENPHAT_THANGTRUOC, " +
                "SUm(SL_TBN_THANG) AS SL_TBN_THANG," +
                "SUM(SL_TBN_THANGTRUOC) AS SL_TBN_THANGTRUOC, " +
                "SUM(SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC) AS SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC, " +
                "SUM(SL_TBN_CUNGKYTHANG_NAMTRUOC) AS SL_TBN_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY MA_CN, MA_BUUCUC";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getSanLuongBCDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getLong("SL_CHUYENPHAT_NGAY"),
                        rs.getLong("SL_LK_CHUYENPHAT_THANG"),
                        rs.getLong("SL_LK_CHUYENPHAT_THANGTRUOC"),
                        rs.getDouble("SL_TBN_THANG"),
                        rs.getDouble("SL_TBN_THANGTRUOC"),
                        rs.getLong("SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC"),
                        rs.getDouble("SL_TBN_CUNGKYTHANG_NAMTRUOC")
                );
                response.add(data);

            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuBC(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, MA_BUUCUC, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "DT_KEHOACH_LK_BC AS DT_KEHOACH_LK_BC, DT_KEHOACH_BC AS SUM_DT_KEHOACH_BC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY MA_CN, MA_BUUCUC, DT_KEHOACH_LK_BC, DT_KEHOACH_BC";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuBCDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_BC"),
                        rs.getDouble("SUM_DT_KEHOACH_BC")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuBCCungKy(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, MA_BUUCUC, " +
                "SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC, " +
                "DT_KEHOACH_LK_BC AS DT_KEHOACH_LK_BC, DT_KEHOACH_BC AS SUM_DT_KEHOACH_BC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') AND MA_KHGUI <> 'PKDQT382' " +
                "GROUP BY MA_CN, MA_BUUCUC, DT_KEHOACH_LK_BC, DT_KEHOACH_BC"; //
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuBCCungKyDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC"),
                        "DoanhThuCungKy"
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }


    private String queryDoanhThuCPBC(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, MA_BUUCUC, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "DT_KEHOACH_LK_BC AS DT_KEHOACH_LK_BC, DT_KEHOACH_BC AS SUM_DT_KEHOACH_BC, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC, " +
                "SUM(DT_TBN_THANG) AS DT_TBN_THANG, SUM(DT_TBN_THANGTRUOC) AS DT_TBN_THANGTRUOC, " +
                "SUM(dt_tbn_cungkythang_namtruoc) AS dt_tbn_cungkythang_namtruoc " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('NON_COD', 'COD', 'EXP') AND LOAI_HANG <> 'HANG_KIEN' " +
                "GROUP BY MA_CN, MA_BUUCUC, DT_KEHOACH_LK_BC, DT_KEHOACH_BC";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuCPBCDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_BC"),
                        rs.getDouble("SUM_DT_KEHOACH_BC"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_TBN_THANG"),
                        rs.getDouble("DT_TBN_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC"),
                        rs.getDouble("dt_tbn_cungkythang_namtruoc")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuVTKhoBC(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, MA_BUUCUC, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "DT_KEHOACH_LK_BC AS DT_KEHOACH_LK_BC, DT_KEHOACH_BC AS SUM_DT_KEHOACH_BC, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('VT', 'KHO', 'FWD') AND LOAI_HANG <> 'HANG_KIEN'  AND MA_KHGUI <> 'PKDQT382' " +
                "GROUP BY MA_CN, MA_BUUCUC, DT_KEHOACH_LK_BC, DT_KEHOACH_BC";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuVTKhoBCDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_BC"),
                        rs.getDouble("SUM_DT_KEHOACH_BC"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuTMUTBC(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, MA_BUUCUC, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, " +
                "DT_KEHOACH_LK_BC AS DT_KEHOACH_LK_BC, DT_KEHOACH_BC AS DT_KEHOACH_BC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') AND MA_KHGUI = 'PKDQT382' " +
                "GROUP BY MA_CN, MA_BUUCUC, DT_KEHOACH_LK_BC, DT_KEHOACH_BC";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuTMUTBCDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "TMUT",
                        rs.getString("MA_CN"),
                        rs.getString("MA_BUUCUC"),
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_BC"),
                        rs.getDouble("DT_KEHOACH_BC")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }



    /** Get doanh thu chi nhanh **/
    private String querySanLuongCN(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, " +
                "SUM(SL_CHUYENPHAT_NGAY) AS SL_CHUYENPHAT_NGAY," +
                "SUM(SL_LK_CHUYENPHAT_THANG) AS SL_LK_CHUYENPHAT_THANG, " +
                "SUM(SL_LK_CHUYENPHAT_THANGTRUOC) AS SL_LK_CHUYENPHAT_THANGTRUOC, " +
                "SUm(SL_TBN_THANG) AS SL_TBN_THANG," +
                "SUM(SL_TBN_THANGTRUOC) AS SL_TBN_THANGTRUOC, " +
                "SUM(SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC) AS SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC, " +
                "SUM(SL_TBN_CUNGKYTHANG_NAMTRUOC) AS SL_TBN_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY MA_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getSanLuongCNDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getLong("SL_CHUYENPHAT_NGAY"),
                        rs.getLong("SL_LK_CHUYENPHAT_THANG"),
                        rs.getLong("SL_LK_CHUYENPHAT_THANGTRUOC"),
                        rs.getDouble("SL_TBN_THANG"),
                        rs.getDouble("SL_TBN_THANGTRUOC"),
                        rs.getLong("SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC"),
                        rs.getDouble("SL_TBN_CUNGKYTHANG_NAMTRUOC")
                );
                response.add(data);

            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuCN(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "DT_KEHOACH_LK_CN AS DT_KEHOACH_LK_CN, DT_KEHOACH_CN AS DT_KEHOACH_CN " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') " +
                "GROUP BY MA_CN, DT_KEHOACH_LK_CN, DT_KEHOACH_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuCNDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_CN"),
                        rs.getDouble("DT_KEHOACH_CN")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuCNCungKy(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, " +
                "SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') AND MA_KHGUI <> 'PKDQT382' " +
                "GROUP BY MA_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuCNCungKyDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC"),
                        "DoanhThuCungKy"
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }


    private String queryDoanhThuCPCN(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "DT_KEHOACH_LK_CN AS DT_KEHOACH_LK_CN, DT_KEHOACH_CN AS DT_KEHOACH_CN, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC, " +
                "SUM(DT_TBN_THANG) AS DT_TBN_THANG, SUM(DT_TBN_THANGTRUOC) AS DT_TBN_THANGTRUOC, " +
                "SUM(dt_tbn_cungkythang_namtruoc) AS dt_tbn_cungkythang_namtruoc " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('NON_COD', 'COD', 'EXP') AND LOAI_HANG <> 'HANG_KIEN' " +
                "GROUP BY MA_CN, DT_KEHOACH_LK_CN, DT_KEHOACH_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuCPCNDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_CN"),
                        rs.getDouble("DT_KEHOACH_CN"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_TBN_THANG"),
                        rs.getDouble("DT_TBN_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC"),
                        rs.getDouble("dt_tbn_cungkythang_namtruoc")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuVTKhoCN(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "DT_KEHOACH_LK_CN AS DT_KEHOACH_LK_CN, DT_KEHOACH_CN AS DT_KEHOACH_CN, " +
                "SUM(DT_LK_NAM) AS DT_LK_NAM, SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('VT', 'KHO', 'FWD') AND LOAI_HANG <> 'HANG_KIEN'  AND MA_KHGUI <> 'PKDQT382' " +
                "GROUP BY MA_CN, DT_KEHOACH_LK_CN, DT_KEHOACH_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuVTKhoCNDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_CN"),
                        rs.getDouble("DT_KEHOACH_CN"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuTMUTCN(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT MA_CN, " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, " +
                "DT_KEHOACH_LK_CN AS DT_KEHOACH_LK_CN, DT_KEHOACH_CN AS DT_KEHOACH_CN " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') AND MA_KHGUI = 'PKDQT382' " +
                "GROUP BY MA_CN, DT_KEHOACH_LK_CN, DT_KEHOACH_CN";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuTMUTCNDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "TMUT",
                        rs.getString("MA_CN"),
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_KEHOACH_LK_CN"),
                        rs.getDouble("DT_KEHOACH_CN")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }



    /** Get doanh thu all chi nhanh **/
    private String querySanLuongAll(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT " +
                "SUM(SL_CHUYENPHAT_NGAY) AS SL_CHUYENPHAT_NGAY," +
                "SUM(SL_LK_CHUYENPHAT_THANG) AS SL_LK_CHUYENPHAT_THANG, " +
                "SUM(SL_LK_CHUYENPHAT_THANGTRUOC) AS SL_LK_CHUYENPHAT_THANGTRUOC, " +
                "SUM(SL_TBN_THANG) AS SL_TBN_THANG, " +
                "SUM(SL_TBN_THANGTRUOC) AS SL_TBN_THANGTRUOC, " +
                "SUM(SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC) AS SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC, " +
                "SUM(SL_TBN_CUNGKYTHANG_NAMTRUOC ) AS SL_TBN_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getSanLuongAllDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "N/A",
                        "N/A",
                        rs.getLong("SL_CHUYENPHAT_NGAY"),
                        rs.getLong("SL_LK_CHUYENPHAT_THANG"),
                        rs.getLong("SL_LK_CHUYENPHAT_THANGTRUOC"),
                        rs.getDouble("SL_TBN_THANG"),
                        rs.getDouble("SL_TBN_THANGTRUOC"),
                        rs.getLong("SL_LK_CHUYENPHAT_CUNGKYTHANG_NAMTRUOC"),
                        rs.getDouble("SL_TBN_CUNGKYTHANG_NAMTRUOC")
                );
                response.add(data);

            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuAll(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "SUM(DT_KEHOACH_LK_CN) AS DT_KEHOACH_LK_CN, SUM(DT_KEHOACH_CN) AS DT_KEHOACH_CN " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuAllDataFromQuery(String ngayBaoCao, String query, List<Double> dtKeHoach) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "N/A",
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        dtKeHoach.get(1),
                        dtKeHoach.get(0)
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuAllCungKy(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT " +
                "SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') AND MA_KHGUI <> 'PKDQT382' ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuAllCungKyDataFromQuery(String ngayBaoCao, String query) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "N/A",
                        "N/A",
                        rs.getDouble("DT_LK_THANG"),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC"),
                        "DoanhThuCungKy"
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }


    private String queryDoanhThuCPAll(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC, " +
                "SUM(DT_TBN_THANG) AS DT_TBN_THANG, SUM(DT_TBN_THANGTRUOC) AS DT_TBN_THANGTRUOC, " +
                "SUM(dt_tbn_cungkythang_namtruoc) AS dt_tbn_cungkythang_namtruoc " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('NON_COD', 'COD', 'EXP') AND LOAI_HANG <> 'HANG_KIEN' ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuCPAllDataFromQuery(String ngayBaoCao, String query, List<Double> dtKeHoachCP) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "N/A",
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        dtKeHoachCP.get(1),
                        dtKeHoachCP.get(0),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_TBN_THANG"),
                        rs.getDouble("DT_TBN_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC"),
                        rs.getDouble("dt_tbn_cungkythang_namtruoc")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuVTKhoAll(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG, SUM(DT_LK_THANGTRUOC) AS DT_LK_THANGTRUOC, " +
                "SUM(DT_KEHOACH_LK_CN) AS DT_KEHOACH_LK_CN, SUM(DT_KEHOACH_CN) AS DT_KEHOACH_CN, " +
                "SUM(DT_LK_NAM) AS DT_LK_NAM, SUM(DT_LK_CUNGKYTHANG_NAMTRUOC) AS DT_LK_CUNGKYTHANG_NAMTRUOC " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('VT', 'KHO', 'FWD') AND LOAI_HANG <> 'HANG_KIEN' AND MA_KHGUI <> 'PKDQT382'";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuVTKhoAllDataFromQuery(String ngayBaoCao, String query, List<Double> dtKeHoachVTKho) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "N/A",
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        dtKeHoachVTKho.get(1),
                        dtKeHoachVTKho.get(0),
                        rs.getDouble("DT_LK_THANGTRUOC"),
                        rs.getDouble("DT_LK_CUNGKYTHANG_NAMTRUOC")
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private String queryDoanhThuTMUTAll(String ngayBaoCao, String table, String maChiSo) {
        String sql = "SELECT " +
                "SUM(DT_NGAY) AS DT_NGAY, SUM(DT_LK_THANG) AS DT_LK_THANG " +
                "FROM %s WHERE version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') AND MA_KHGUI = 'PKDQT382' ";
        sql = String.format(sql, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        System.out.println("--------------------sql " + sql);
        return sql;
    }

    private List<SMSDoanhThu> getDoanhThuTMUTAllDataFromQuery(String ngayBaoCao, String query, List<Double> dtKeHoachTMUT) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<SMSDoanhThu> response = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(query);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SMSDoanhThu data = new SMSDoanhThu(
                        ngayBaoCao,
                        "TMUT",
                        "N/A",
                        "N/A",
                        rs.getDouble("DT_NGAY"),
                        rs.getDouble("DT_LK_THANG"),
                        dtKeHoachTMUT.get(1),
                        dtKeHoachTMUT.get(0)
                );
                response.add(data);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    List<Double> getDoanhThuKeHoach(String ngayBaoCao, String table, String maChiSo) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        List<Double> response = new ArrayList<>();
        Double doanhThuKeHoach = (double) 0;
        Double doanhThuKeHoachLK = (double) 0;
        String sqlDoanhThuKH = "select sum(dt_kehoach_cn) as dt_kehoach, sum(dt_kehoach_lk_cn) as dt_kehoach_lk from " +
                "(select ma_cn, nhom_dv, max(dt_kehoach_cn) as dt_kehoach_cn, max(dt_kehoach_lk_cn) as dt_kehoach_lk_cn " +
                "from %s where version = %s AND NGAY_BAOCAO = '%s' AND LOAI_HANG <> 'HANG_KIEN' AND NHOM_DV NOT IN ('COD', 'NON_COD') group by ma_cn, nhom_dv)";
        sqlDoanhThuKH = String.format(sqlDoanhThuKH, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlDoanhThuKH);
            System.out.println("--------------------sql" + sqlDoanhThuKH);
            rs = stmt.executeQuery();
            while (rs.next()) {
                doanhThuKeHoach = rs.getDouble("dt_kehoach");
                response.add(doanhThuKeHoach);
                doanhThuKeHoachLK = rs.getDouble("dt_kehoach_lk");
                response.add(doanhThuKeHoachLK);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    List<Double> getDoanhThuKeHoachCP(String ngayBaoCao, String table, String maChiSo) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        List<Double> response = new ArrayList<>();
        Double doanhThuKeHoach = (double) 0;
        Double doanhThuKeHoachLK = (double) 0;
        String sqlDoanhThuKH = "select sum(dt_kehoach_cn) as dt_kehoach, sum(dt_kehoach_lk_cn) as dt_kehoach_lk from " +
                "(select ma_cn, nhom_dv, max(dt_kehoach_cn) as  dt_kehoach_cn, max(dt_kehoach_lk_cn) as dt_kehoach_lk_cn " +
                "from %s where version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('NON_COD', 'COD', 'EXP') AND LOAI_HANG <> 'HANG_KIEN' group by ma_cn, nhom_dv)";
        sqlDoanhThuKH = String.format(sqlDoanhThuKH, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlDoanhThuKH);
            System.out.println("--------------------sql" + sqlDoanhThuKH);
            rs = stmt.executeQuery();
            while (rs.next()) {
                doanhThuKeHoach = rs.getDouble("dt_kehoach");
                response.add(doanhThuKeHoach);
                doanhThuKeHoachLK = rs.getDouble("dt_kehoach_lk");
                response.add(doanhThuKeHoachLK);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    List<Double> getDoanhThuKeHoachVTKho(String ngayBaoCao, String table, String maChiSo) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        List<Double> response = new ArrayList<>();
        Double doanhThuKeHoach = (double) 0;
        Double doanhThuKeHoachLK = (double) 0;
        String sqlDoanhThuKH = "select sum(dt_kehoach_cn) as dt_kehoach, sum(dt_kehoach_lk_cn) as dt_kehoach_lk from " +
                "(select ma_cn, nhom_dv, max(dt_kehoach_cn) as dt_kehoach_cn, max(dt_kehoach_lk_cn) as dt_kehoach_lk_cn " +
                "from %s where version = %s AND NGAY_BAOCAO = '%s' AND NHOM_DV in('VT', 'KHO', 'FWD') AND LOAI_HANG <> 'HANG_KIEN' group by ma_cn, nhom_dv)";
        sqlDoanhThuKH = String.format(sqlDoanhThuKH, table, getVersionMax(maChiSo, ngayBaoCao), ngayBaoCao);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlDoanhThuKH);
            System.out.println("--------------------sql" + sqlDoanhThuKH);
            rs = stmt.executeQuery();
            while (rs.next()) {
                doanhThuKeHoach = rs.getDouble("dt_kehoach");
                response.add(doanhThuKeHoach);
                doanhThuKeHoachLK = rs.getDouble("dt_kehoach_lk");
                response.add(doanhThuKeHoachLK);
            }
        } catch (Exception e) {
            System.out.println("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    List<Double> getDoanhThuKeHoachTMUT(Configuration config, String ngayBaoCao, String table) throws IOException {
        List<Double> response = new ArrayList<>();
        Double doanhThuKeHoach = (double) 0;
        Double doanhThuKeHoachLK = (double) 0;

        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(ngayBaoCao, inputFormatter);

        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMM");
        String formattedDateStr = localDate.format(outputFormatter);

        String sqlDoanhThuKH = "select sum(chi_tieu_giao) as chi_tieu_giao from " +
                "(select ma_cn, max(chi_tieu_giao) as chi_tieu_giao " +
                "from %s where thang = '%s' and type = 1 and dich_vu = '%s' group by ma_cn) as a";
        sqlDoanhThuKH = String.format(sqlDoanhThuKH, table, formattedDateStr, Const.TMUT);
        System.out.println("sqlDoanhThuKH " + sqlDoanhThuKH);
        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_QUALITY"), config.getConfig("POSTGRES.USER_QUALITY"), config.getConfig("POSTGRES.PASSWD_QUALITY"))) {
            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(sqlDoanhThuKH)) {
                try (ResultSet resultSet = pstmt.executeQuery()) {
                    while (resultSet.next()) {
                        doanhThuKeHoach = resultSet.getDouble("chi_tieu_giao");
                        response.add(doanhThuKeHoach);
                    }
                }
            } catch (SQLException ex) {
                System.out.println("SQLException" + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException e) {
                    System.out.println("SQLException" + e.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }

        List<Double> ngayQuyDoiValue = getNgayQuyDoi(ngayBaoCao);
        if (doanhThuKeHoach != null && ngayQuyDoiValue.size() == 2) {
            doanhThuKeHoachLK = ((doanhThuKeHoach * ngayQuyDoiValue.get(0)) / ngayQuyDoiValue.get(1));
            response.add(doanhThuKeHoachLK);
        }
        return response;
    }

    private List<Double> getNgayQuyDoi(String ngayBaoCao) throws IOException {
        /** Tính toán doanh thu kế hoạch LK ngày **/
        List<Double> response = new ArrayList<>();

        String filePath = "files/quy_doi_ngay.xlsx";
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(ngayBaoCao, inputFormatter);

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("M/d/yyyy");
        String formattedDate = localDate.format(dateFormatter);
        LocalDate targetDate = LocalDate.parse(formattedDate, dateFormatter);

        InputStream inputStream = null;
        try {
            ClassLoader classLoader = this.getClass().getClassLoader();
            inputStream = classLoader.getResourceAsStream(filePath);
            if (inputStream == null) {
                throw new IOException("File không tồn tại: " + filePath);
            }
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            // Lấy sheet đầu tiên
            Sheet sheet = workbook.getSheetAt(0);

            // Tìm cột "NGAY"
            Row headerRow = sheet.getRow(0);
            int ngayColumnIndex = -1;
            int ngayQuyDoiLkThangColumnIndex = -1;
            int ngayQuyDoiThangColumnIndex = -1;
            for (Cell cell : headerRow) {
                String cellValue = cell.getStringCellValue();
                if ("NGAY".equalsIgnoreCase(cellValue)) {
                    ngayColumnIndex = cell.getColumnIndex();
                } else if ("NGAY_QUYDOI_LK_THANG".equalsIgnoreCase(cellValue)) {
                    ngayQuyDoiLkThangColumnIndex = cell.getColumnIndex();
                } else if ("NGAY_QUYDOI_THANG".equalsIgnoreCase(cellValue)) {
                    ngayQuyDoiThangColumnIndex = cell.getColumnIndex();
                }
            }

            if (ngayColumnIndex == -1 || ngayQuyDoiLkThangColumnIndex == -1 || ngayQuyDoiThangColumnIndex == -1) {
                System.out.println("Không tìm thấy một trong các cột yêu cầu.");
            }
            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue;
                }
                Cell cell = row.getCell(ngayColumnIndex);
                if (cell != null && cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell) ) {
                    LocalDate cellDate = cell.getLocalDateTimeCellValue().toLocalDate();
                    if (cellDate.equals(targetDate)) {
                        Cell ngayQuyDoiLkThangCell = row.getCell(ngayQuyDoiLkThangColumnIndex);
                        Cell ngayQuyDoiThangCell = row.getCell(ngayQuyDoiThangColumnIndex);

                        double  ngayQuyDoiLkThangValue = getCellValueAsDouble(ngayQuyDoiLkThangCell);
                        response.add(ngayQuyDoiLkThangValue);
                        double  ngayQuyDoiThangValue = getCellValueAsDouble(ngayQuyDoiThangCell);
                        response.add(ngayQuyDoiThangValue);
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            inputStream.close();
        }
        return response;
    }

    private static double getCellValueAsDouble(Cell cell) {
        if (cell == null) {
            return 0.0;
        }
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                try {
                    return Double.parseDouble(cell.getStringCellValue());
                } catch (NumberFormatException e) {
                    return 0.0;
                }
            default:
                return 0.0;
        }
    }





}
