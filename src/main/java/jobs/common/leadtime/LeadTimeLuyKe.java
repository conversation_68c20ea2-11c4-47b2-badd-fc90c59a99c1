package jobs.common.leadtime;

import configs.Configuration;
import model.leadtime.LeadTimeLkThang;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class LeadTimeLuyKe {
//    private static final Logger LOGGER = LogManager.getLogger(LeadTimeLuyKe.class);
    public static List<LeadTimeLkThang> getLuyKeLeadTime(Configuration config, String firstDayOfMonth, String yesterday) {
//        LOGGER.trace("firstDayOfMonth : " +  firstDayOfMonth + "dateEnd : " + yesterday);
        List<LeadTimeLkThang> leadTimeList = new ArrayList<>();
        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_INDEX"), config.getConfig("POSTGRES.USER_INDEX"), config.getConfig("POSTGRES.PASSWD_INDEX"))) {
            String sqlQuery = "SELECT" +
                    "    '%s' AS ngayBaocao," +
                    "    ma_chinhanh AS maChiNhanh," +
                    "    ma_buucuc AS maBuuCuc," +
                    "    (SUM(tg_tt_all * sl_don_all) / (CASE WHEN SUM(sl_don_all) <> 0 THEN SUM(sl_don_all) ELSE 1 END)) AS tgTtAll," +
                    "    (SUM(tg_fm_all * sl_fm_all) / (CASE WHEN SUM(sl_fm_all) <> 0 THEN SUM(sl_fm_all) ELSE 1 END)) AS tgFmAll," +
                    "    (SUM(tg_mm_all * sl_don_all) / (CASE WHEN SUM(sl_don_all) <> 0 THEN SUM(sl_don_all) ELSE 1 END)) AS tgMmAll," +
                    "    (SUM(tg_lm_all * sl_don_all) / (CASE WHEN SUM(sl_don_all) <> 0 THEN SUM(sl_don_all) ELSE 1 END)) AS tgLmAll," +
                    "    (SUM(tg_noitinh_all * sl_noitinh_all) / (CASE WHEN SUM(sl_noitinh_all) <> 0 THEN SUM(sl_noitinh_all) ELSE 1 END)) AS tgNoiTinhAll," +
                    "    (SUM(tg_noimien_all * sl_noimien_all) / (CASE WHEN SUM(sl_noimien_all) <> 0 THEN SUM(sl_noimien_all) ELSE 1 END)) AS tgNoiMienAll," +
                    "    (SUM(tg_lienmien_all * sl_lienmien_all) / (CASE WHEN SUM(sl_lienmien_all) <> 0 THEN SUM(sl_lienmien_all) ELSE 1 END)) AS tgLienMienAll," +
                    "    (SUM(tg_tt_nhanh * sl_tt_nhanh) / (CASE WHEN SUM(sl_tt_nhanh) <> 0 THEN SUM(sl_tt_nhanh) ELSE 1 END)) AS tgTtNhanh," +
                    "    (SUM(tg_fm_nhanh * sl_fm_nhanh) / (CASE WHEN SUM(sl_fm_nhanh) <> 0 THEN SUM(sl_fm_nhanh) ELSE 1 END)) AS tgFmNhanh," +
                    "    (SUM(tg_mm_nhanh * sl_mm_nhanh) / (CASE WHEN SUM(sl_mm_nhanh) <> 0 THEN SUM(sl_mm_nhanh) ELSE 1 END)) AS tgMmNhanh," +
                    "    (SUM(tg_lm_nhanh * sl_lm_nhanh) / (CASE WHEN SUM(sl_lm_nhanh) <> 0 THEN SUM(sl_lm_nhanh) ELSE 1 END)) AS tgLmNhanh," +
                    "    (SUM(tg_noitinh_nhanh * sl_noitinh_nhanh) / (CASE WHEN SUM(sl_noitinh_nhanh) <> 0 THEN SUM(sl_noitinh_nhanh) ELSE 1 END)) AS tgNoiTinhNhanh," +
                    "    (SUM(tg_noimien_nhanh * sl_noimien_nhanh) / (CASE WHEN SUM(sl_noimien_nhanh) <> 0 THEN SUM(sl_noimien_nhanh) ELSE 1 END)) AS tgNoiMienNhanh," +
                    "    (SUM(tg_lienmien_nhanh * sl_lienmien_nhanh) / (CASE WHEN SUM(sl_lienmien_nhanh) <> 0 THEN SUM(sl_lienmien_nhanh) ELSE 1 END)) AS tgLienMienNhanh," +
                    "    (SUM(tg_tt_tk * sl_tt_tk) / (CASE WHEN SUM(sl_tt_tk) <> 0 THEN SUM(sl_tt_tk) ELSE 1 END)) AS tgTtTk," +
                    "    (SUM(tg_fm_tk * sl_fm_tk) / (CASE WHEN SUM(sl_fm_tk) <> 0 THEN SUM(sl_fm_tk) ELSE 1 END)) AS tgFmTk," +
                    "    (SUM(tg_mm_tk * sl_mm_tk) / (CASE WHEN SUM(sl_mm_tk) <> 0 THEN SUM(sl_mm_tk) ELSE 1 END)) AS tgMmTk," +
                    "    (SUM(tg_lm_tk * sl_lm_tk) / (CASE WHEN SUM(sl_lm_tk) <> 0 THEN SUM(sl_lm_tk) ELSE 1 END)) AS tgLmTk," +
                    "    (SUM(tg_noitinh_tk * sl_noitinh_tk) / (CASE WHEN SUM(sl_noitinh_tk) <> 0 THEN SUM(sl_noitinh_tk) ELSE 1 END)) AS tgNoiTinhTk," +
                    "    (SUM(tg_noimien_tk * sl_noimien_tk) / (CASE WHEN SUM(sl_noimien_tk) <> 0 THEN SUM(sl_noimien_tk) ELSE 1 END)) AS tgNoiMienTk," +
                    "    (SUM(tg_lienmien_tk * sl_lienmien_tk) / (CASE WHEN SUM(sl_lienmien_tk) <> 0 THEN SUM(sl_lienmien_tk) ELSE 1 END)) AS tgLienMienTk," +
                    "    (SUM(tg_tt_kien_tk * sl_tt_kien_tk) / (CASE WHEN SUM(sl_tt_kien_tk) <> 0 THEN SUM(sl_tt_kien_tk) ELSE 1 END)) AS tgTtKienTk," +
                    "    (SUM(tg_fm_kien_tk * sl_fm_kien_tk) / (CASE WHEN SUM(sl_fm_kien_tk) <> 0 THEN SUM(sl_fm_kien_tk) ELSE 1 END)) AS tgFmKienTk," +
                    "    (SUM(tg_mm_kien_tk * sl_mm_kien_tk) / (CASE WHEN SUM(sl_mm_kien_tk) <> 0 THEN SUM(sl_mm_kien_tk) ELSE 1 END)) AS tgMmKienTk," +
                    "    (SUM(tg_lm_kien_tk * sl_lm_kien_tk) / (CASE WHEN SUM(sl_lm_kien_tk) <> 0 THEN SUM(sl_lm_kien_tk) ELSE 1 END)) AS tgLmKienTk," +
                    "    (SUM(tg_noitinh_kien_tk * sl_noitinh_kien_tk) / (CASE WHEN SUM(sl_noitinh_kien_tk) <> 0 THEN SUM(sl_noitinh_kien_tk) ELSE 1 END)) AS tgNoiTinhKienTk," +
                    "    (SUM(tg_noimien_kien_tk * sl_noimien_kien_tk) / (CASE WHEN SUM(sl_noimien_kien_tk) <> 0 THEN SUM(sl_noimien_kien_tk) ELSE 1 END)) AS tgNoiMienKienTk," +
                    "    (SUM(tg_lienmien_kien_tk * sl_lienmien_kien_tk) / (CASE WHEN SUM(sl_lienmien_kien_tk) <> 0 THEN SUM(sl_lienmien_kien_tk) ELSE 1 END)) AS tgLienMienKienTk," +
                    "    (SUM(tg_tt_tach_kien_tk * sl_tt_tach_kien_tk) / (CASE WHEN SUM(sl_tt_tach_kien_tk) <> 0 THEN SUM(sl_tt_tach_kien_tk) ELSE 1 END)) AS tgTtTachKienTk," +
                    "    (SUM(tg_fm_tach_kien_tk * sl_fm_tach_kien_tk) / (CASE WHEN SUM(sl_fm_tach_kien_tk) <> 0 THEN SUM(sl_fm_tach_kien_tk) ELSE 1 END)) AS tgFmTachKienTk," +
                    "    (SUM(tg_mm_tach_kien_tk * sl_mm_tach_kien_tk) / (CASE WHEN SUM(sl_mm_tach_kien_tk) <> 0 THEN SUM(sl_mm_tach_kien_tk) ELSE 1 END)) AS tgMmTachKienTk," +
                    "    (SUM(tg_lm_tach_kien_tk * sl_lm_tach_kien_tk) / (CASE WHEN SUM(sl_lm_tach_kien_tk) <> 0 THEN SUM(sl_lm_tach_kien_tk) ELSE 1 END)) AS tgLmTachKienTk," +
                    "    (SUM(tg_noitinh_tach_kien_tk * sl_noitinh_tach_kien_tk) / (CASE WHEN SUM(sl_noitinh_tach_kien_tk) <> 0 THEN SUM(sl_noitinh_tach_kien_tk) ELSE 1 END)) AS tgNoiTinhTachKienTk," +
                    "    (SUM(tg_noimien_tach_kien_tk * sl_noimien_tach_kien_tk) / (CASE WHEN SUM(sl_noimien_tach_kien_tk) <> 0 THEN SUM(sl_noimien_tach_kien_tk) ELSE 1 END)) AS tgNoiMienTachKienTk," +
                    "    (SUM(tg_lienmien_tach_kien_tk * sl_lienmien_tach_kien_tk) / (CASE WHEN SUM(sl_lienmien_tach_kien_tk) <> 0 THEN SUM(sl_lienmien_tach_kien_tk) ELSE 1 END)) AS tgLienMienTachKienTk," +
                    "    (SUM(tg_mm_noimien * sl_noimien_all) / (CASE WHEN SUM(sl_noimien_all) <> 0 THEN SUM(sl_noimien_all) ELSE 1 END)) AS tgMmNoiMien," +
                    "    (SUM(tg_mm_lienmien * sl_lienmien_all) / (CASE WHEN SUM(sl_lienmien_all) <> 0 THEN SUM(sl_lienmien_all) ELSE 1 END)) AS tgMmLienMien" +
                    " FROM " +
                    "    dash_tct_leadtime_ngay" +
                    " WHERE " +
                    "    ngay_baocao BETWEEN '%s' AND '%s'" +
                    " GROUP BY " +
                    "    ma_chinhanh, ma_buucuc;";
            sqlQuery = String.format(sqlQuery, yesterday, firstDayOfMonth, yesterday);

            try (PreparedStatement preparedStatement = connection.prepareStatement(sqlQuery)) {
                try (ResultSet resultSet = preparedStatement.executeQuery()) {
                    while (resultSet.next()) {
                        LeadTimeLkThang leadTime = new LeadTimeLkThang();
                        leadTime.setNgayBaoCao(String.valueOf(resultSet.getDate("ngayBaocao")));
                        leadTime.setMaChiNhanh(resultSet.getString("maChiNhanh"));
                        leadTime.setMaBuuCuc(resultSet.getString("maBuuCuc"));
                        leadTime.setTgTtAll(resultSet.getDouble("tgTtAll"));
                        leadTime.setTgFmAll(resultSet.getDouble("tgFmAll"));
                        leadTime.setTgMmAll(resultSet.getDouble("tgMmAll"));
                        leadTime.setTgLmAll(resultSet.getDouble("tgLmAll"));
                        leadTime.setTgNoiTinhAll(resultSet.getDouble("tgNoiTinhAll"));
                        leadTime.setTgNoiMienAll(resultSet.getDouble("tgNoiMienAll"));
                        leadTime.setTgLienMienAll(resultSet.getDouble("tgLienMienAll"));
                        leadTime.setTgTtNhanh(resultSet.getDouble("tgTtNhanh"));
                        leadTime.setTgFmNhanh(resultSet.getDouble("tgFmNhanh"));
                        leadTime.setTgMmNhanh(resultSet.getDouble("tgMmNhanh"));
                        leadTime.setTgLmNhanh(resultSet.getDouble("tgLmNhanh"));
                        leadTime.setTgNoiTinhNhanh(resultSet.getDouble("tgNoiTinhNhanh"));
                        leadTime.setTgNoiMienNhanh(resultSet.getDouble("tgNoiMienNhanh"));
                        leadTime.setTgLienMienNhanh(resultSet.getDouble("tgLienMienNhanh"));
                        leadTime.setTgTtTk(resultSet.getDouble("tgTtTk"));
                        leadTime.setTgFmTk(resultSet.getDouble("tgFmTk"));
                        leadTime.setTgMmTk(resultSet.getDouble("tgMmTk"));
                        leadTime.setTgLmTk(resultSet.getDouble("tgLmTk"));
                        leadTime.setTgNoiTinhTk(resultSet.getDouble("tgNoiTinhTk"));
                        leadTime.setTgNoiMienTk(resultSet.getDouble("tgNoiMienTk"));
                        leadTime.setTgLienMienTk(resultSet.getDouble("tgLienMienTk"));
                        leadTime.setTgTtKienTk(resultSet.getDouble("tgTtKienTk"));
                        leadTime.setTgFmKienTk(resultSet.getDouble("tgFmKienTk"));
                        leadTime.setTgMmKienTk(resultSet.getDouble("tgMmKienTk"));
                        leadTime.setTgLmKienTk(resultSet.getDouble("tgLmKienTk"));
                        leadTime.setTgNoiTinhKienTk(resultSet.getDouble("tgNoiTinhKienTk"));
                        leadTime.setTgNoiMienKienTk(resultSet.getDouble("tgNoiMienKienTk"));
                        leadTime.setTgLienMienKienTk(resultSet.getDouble("tgLienMienKienTk"));
                        leadTime.setTgTtTachKienTk(resultSet.getDouble("tgTtTachKienTk"));
                        leadTime.setTgFmTachKienTk(resultSet.getDouble("tgFmTachKienTk"));
                        leadTime.setTgMmTachKienTk(resultSet.getDouble("tgMmTachKienTk"));
                        leadTime.setTgLmTachKienTk(resultSet.getDouble("tgLmTachKienTk"));
                        leadTime.setTgNoiTinhTachKienTk(resultSet.getDouble("tgNoiTinhTachKienTk"));
                        leadTime.setTgNoiMienTachKienTk(resultSet.getDouble("tgNoiMienTachKienTk"));
                        leadTime.setTgLienMienTachKienTk(resultSet.getDouble("tgLienMienTachKienTk"));
                        leadTime.setTgMmNoiMien(resultSet.getDouble("tgMmNoiMien"));
                        leadTime.setTgMmLienMien(resultSet.getDouble("tgMmLienMien"));
                        leadTimeList.add(leadTime);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return leadTimeList;
    }
}
