package jobs.common.leadtime;

import configs.Configuration;
import connection.PhoenixConnectionFactory;
import model.leadtime.LeadTimeAllDto;
import model.leadtime.LeadtimeFmDto;
import model.leadtime.LeadtimeMmLmDto;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import utils.AbstractDao;
import utils.Utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class LeadTimeNgay extends AbstractDao {
    protected static final Logger eLogger = LogManager.getLogger("ErrorLog");
    public static final String MA_CHI_SO = "kpi_phandoan";

    public static List<LeadTimeAllDto> calculateLeadTimeDate(String ngayBaocao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<LeadtimeMmLmDto> leadtimeMmLmDtos = new ArrayList<>();
        List<LeadtimeFmDto> leadtimeFmDtos = new ArrayList<>();
        List<LeadTimeAllDto> leadTimeAllDtos = new ArrayList<>();
        String queryMmLm = getQueryKhauMmLM(ngayBaocao);
        String queryFm = getQueryLM(ngayBaocao);

        try {
            //connection query
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            //khâu Mm và khâu lm query riêng theo chi nhanh phát, bưu cục phát
            stmt = conn.prepareStatement(queryMmLm);
            rs = stmt.executeQuery();
            while (rs.next()) {
                LeadtimeMmLmDto leadtimeMmLmDto = new LeadtimeMmLmDto();
                leadtimeMmLmDto.setNgayBaoCao(ngayBaocao);
                // Ánh xạ giá trị từ cột "chinhanh_phat" vào thuộc tính "ma_chinhanh"
                leadtimeMmLmDto.setMaChiNhanh(rs.getString("ma_chinhanh"));
                // Ánh xạ giá trị từ cột "buucuc_phat" vào thuộc tính "ma_buucuc"
                leadtimeMmLmDto.setMaBuuCuc(rs.getString("ma_buucuc"));
                // Ánh xạ các giá trị trung bình vào các thuộc tính tương ứng
                leadtimeMmLmDto.setTgTtAll(Utils.round(rs.getFloat("tg_tt_all")));
                leadtimeMmLmDto.setTgMmAll(Utils.round(rs.getFloat("tg_mm_all")));
                leadtimeMmLmDto.setTgLmAll(Utils.round(rs.getFloat("tg_lm_all")));
                leadtimeMmLmDto.setTgNoiTinhAll(Utils.round(rs.getFloat("tg_noitinh_all")));
                leadtimeMmLmDto.setTgNoiMienAll(Utils.round(rs.getFloat("tg_noimien_all")));
                leadtimeMmLmDto.setTgLienMienAll(Utils.round(rs.getFloat("tg_lienmien_all")));

                leadtimeMmLmDto.setTgTtNhanh(Utils.round(rs.getFloat("tg_tt_nhanh")));
                leadtimeMmLmDto.setTgMmNhanh(Utils.round(rs.getFloat("tg_mm_nhanh")));
                leadtimeMmLmDto.setTgLmNhanh(Utils.round(rs.getFloat("tg_lm_nhanh")));
                leadtimeMmLmDto.setTgNoiTinhNhanh(Utils.round(rs.getFloat("tg_noitinh_nhanh")));
                leadtimeMmLmDto.setTgNoiMienNhanh(Utils.round(rs.getFloat("tg_noimien_nhanh")));
                leadtimeMmLmDto.setTgLienMienNhanh(Utils.round(rs.getFloat("tg_lienmien_nhanh")));

                leadtimeMmLmDto.setTgTtTk(Utils.round(rs.getFloat("tg_tt_tk")));
                leadtimeMmLmDto.setTgMmTk(Utils.round(rs.getFloat("tg_mm_tk")));
                leadtimeMmLmDto.setTgLmTk(Utils.round(rs.getFloat("tg_lm_tk")));
                leadtimeMmLmDto.setTgNoiTinhTk(Utils.round(rs.getFloat("tg_noitinh_tk")));
                leadtimeMmLmDto.setTgNoiMienTk(Utils.round(rs.getFloat("tg_noimien_tk")));
                leadtimeMmLmDto.setTgLienMienTk(Utils.round(rs.getFloat("tg_lienmien_tk")));

                leadtimeMmLmDto.setTgTtKienTk(Utils.round(rs.getFloat("tg_tt_kien_tk")));
                leadtimeMmLmDto.setTgMmKienTk(Utils.round(rs.getFloat("tg_mm_kien_tk")));
                leadtimeMmLmDto.setTgLmKienTk(Utils.round(rs.getFloat("tg_lm_kien_tk")));
                leadtimeMmLmDto.setTgNoiTinhKienTk(Utils.round(rs.getFloat("tg_noitinh_kien_tk")));
                leadtimeMmLmDto.setTgNoiMienKienTk(Utils.round(rs.getFloat("tg_noimien_kien_tk")));
                leadtimeMmLmDto.setTgLienMienKienTk(Utils.round(rs.getFloat("tg_lienmien_kien_tk")));

                leadtimeMmLmDto.setTgTtTachKienTk(Utils.round(rs.getFloat("tg_tt_tach_kien_tk")));
                leadtimeMmLmDto.setTgMmTachKienTk(Utils.round(rs.getFloat("tg_mm_tach_kien_tk")));
                leadtimeMmLmDto.setTgLmTachKienTk(Utils.round(rs.getFloat("tg_lm_tach_kien_tk")));
                leadtimeMmLmDto.setTgNoiTinhTachKienTk(Utils.round(rs.getFloat("tg_noitinh_tach_kien_tk")));
                leadtimeMmLmDto.setTgNoiMienTachKienTk(Utils.round(rs.getFloat("tg_noimien_tach_kien_tk")));
                leadtimeMmLmDto.setTgLienMienTachKienTk(Utils.round(rs.getFloat("tg_lienmien_tach_kien_tk")));
                // So luong

                leadtimeMmLmDto.setSlDonAll(rs.getLong("sl_don_all"));
                leadtimeMmLmDto.setSlNoiTinhAll(rs.getLong("sl_noitinh_all"));
                leadtimeMmLmDto.setSlNoiMienAll(rs.getLong("sl_noimien_all"));
                leadtimeMmLmDto.setSlLienMienAll(rs.getLong("sl_lienmien_all"));
                leadtimeMmLmDto.setSlTtNhanh(rs.getLong("sl_tt_nhanh"));
                leadtimeMmLmDto.setSlMmNhanh(rs.getLong("sl_mm_nhanh"));
                leadtimeMmLmDto.setSlLmNhanh(rs.getLong("sl_lm_nhanh"));
                leadtimeMmLmDto.setSlNoiTinhNhanh(rs.getLong("sl_noitinh_nhanh"));
                leadtimeMmLmDto.setSlNoiMienNhanh(rs.getLong("sl_noimien_nhanh"));
                leadtimeMmLmDto.setSlLienMienNhanh(rs.getLong("sl_lienmien_nhanh"));
                leadtimeMmLmDto.setSlTtTk(rs.getLong("sl_tt_tk"));
                leadtimeMmLmDto.setSlMmTk(rs.getLong("sl_mm_tk"));
                leadtimeMmLmDto.setSlLmTk(rs.getLong("sl_lm_tk"));
                leadtimeMmLmDto.setSlNoiTinhTk(rs.getLong("sl_noitinh_tk"));
                leadtimeMmLmDto.setSlNoiMienTk(rs.getLong("sl_noimien_tk"));
                leadtimeMmLmDto.setSlLienMienTk(rs.getLong("sl_lienmien_tk"));
                leadtimeMmLmDto.setSlTtKienTk(rs.getLong("sl_tt_kien_tk"));
                leadtimeMmLmDto.setSlMmKienTk(rs.getLong("sl_mm_kien_tk"));
                leadtimeMmLmDto.setSlLmKienTk(rs.getLong("sl_lm_kien_tk"));
                leadtimeMmLmDto.setSlNoiTinhKienTk(rs.getLong("sl_noitinh_kien_tk"));
                leadtimeMmLmDto.setSlNoiMienKienTk(rs.getLong("sl_noimien_kien_tk"));
                leadtimeMmLmDto.setSlLienMienKienTk(rs.getLong("sl_lienmien_kien_tk"));
                leadtimeMmLmDto.setSlTtTachKienTk(rs.getLong("sl_tt_tach_kien_tk"));
                leadtimeMmLmDto.setSlMmTachKienTk(rs.getLong("sl_mm_tach_kien_tk"));
                leadtimeMmLmDto.setSlLmTachKienTk(rs.getLong("sl_lm_tach_kien_tk"));
                leadtimeMmLmDto.setSlNoiTinhTachKienTk(rs.getLong("sl_noitinh_tach_kien_tk"));
                leadtimeMmLmDto.setSlNoiMienTachKienTk(rs.getLong("sl_noimien_tach_kien_tk"));
                leadtimeMmLmDto.setSlLienMienTachKienTk(rs.getLong("sl_lienmien_tach_kien_tk"));

                // thêm 2 cột để tính middle mile
                leadtimeMmLmDto.setTgMmNoiMien(Utils.round(rs.getFloat("tg_mm_noimien")));
                leadtimeMmLmDto.setTgMmLienMien(Utils.round(rs.getFloat("tg_mm_lienmien")));

                leadtimeMmLmDtos.add(leadtimeMmLmDto);
            }
            // xu ly FM
            stmt = conn.prepareStatement(queryFm);
            rs = stmt.executeQuery();
            while (rs.next()) {
                LeadtimeFmDto leadtimeFmDto = new LeadtimeFmDto();
                leadtimeFmDto.setNgayBaoCao(ngayBaocao);
                // Ánh xạ giá trị từ cột "chinhanh_phat" vào thuộc tính "ma_chinhanh"
                leadtimeFmDto.setMaChiNhanh(rs.getString("ma_chinhanh"));
                // Ánh xạ giá trị từ cột "buucuc_phat" vào thuộc tính "ma_buucuc"
                leadtimeFmDto.setMaBuuCuc(rs.getString("ma_buucuc"));
                // Ánh xạ các giá trị trung bình vào các thuộc tính tương ứng
                leadtimeFmDto.setTgFmAll(Utils.round(rs.getFloat("tg_fm_all")));
                leadtimeFmDto.setTgFmNhanh(Utils.round(rs.getFloat("tg_fm_nhanh")));
                leadtimeFmDto.setTgFmTk(Utils.round(rs.getFloat("tg_fm_tk")));
                leadtimeFmDto.setTgFmKienTk(Utils.round(rs.getFloat("tg_fm_kien_tk")));
                leadtimeFmDto.setTgFmTachKienTk(Utils.round(rs.getFloat("tg_fm_tach_kien_tk")));
                // so lương
                leadtimeFmDto.setSlFmAll(rs.getLong("sl_fm_all"));
                leadtimeFmDto.setSlFmNhanh(rs.getLong("sl_fm_nhanh"));
                leadtimeFmDto.setSlFmTk(rs.getLong("sl_fm_tk"));
                leadtimeFmDto.setSlFmKienTk(rs.getLong("sl_fm_kien_tk"));
                leadtimeFmDto.setSlFmTachKienTk(rs.getLong("sl_fm_tach_kien_tk"));
                leadtimeFmDtos.add(leadtimeFmDto);
            }

            // map 2 list vao voi nhau tao ra bang hoan chinh
            for (LeadtimeMmLmDto mmLmDto : leadtimeMmLmDtos) {
                // Tạo một đối tượng LeadTimeAllDto mới
                LeadTimeAllDto leadTimeAllDto = new LeadTimeAllDto();
                // Thiết lập các thuộc tính chung từ LeadtimeMmLmDto
                leadTimeAllDto.setNgayBaoCao(mmLmDto.getNgayBaoCao());
                leadTimeAllDto.setMaChiNhanh(mmLmDto.getMaChiNhanh());
                leadTimeAllDto.setMaBuuCuc(mmLmDto.getMaBuuCuc());
                //All
                leadTimeAllDto.setTgTtAll(mmLmDto.getTgTtAll());
                leadTimeAllDto.setTgMmAll(mmLmDto.getTgMmAll());
                leadTimeAllDto.setTgLmAll(mmLmDto.getTgLmAll());
                leadTimeAllDto.setTgNoiTinhAll(mmLmDto.getTgNoiTinhAll());
                leadTimeAllDto.setTgNoiMienAll(mmLmDto.getTgNoiMienAll());
                leadTimeAllDto.setTgLienMienAll(mmLmDto.getTgLienMienAll());
                //Nhanh
                leadTimeAllDto.setTgTtNhanh(mmLmDto.getTgTtNhanh());
                leadTimeAllDto.setTgMmNhanh(mmLmDto.getTgMmNhanh());
                leadTimeAllDto.setTgLmNhanh(mmLmDto.getTgLmNhanh());
                leadTimeAllDto.setTgNoiTinhNhanh(mmLmDto.getTgNoiTinhNhanh());
                leadTimeAllDto.setTgNoiMienNhanh(mmLmDto.getTgNoiMienNhanh());
                leadTimeAllDto.setTgLienMienNhanh(mmLmDto.getTgLienMienNhanh());
                // Tiet kiem
                leadTimeAllDto.setTgTtTk(mmLmDto.getTgTtTk());
                leadTimeAllDto.setTgMmTk(mmLmDto.getTgMmTk());
                leadTimeAllDto.setTgLmTk(mmLmDto.getTgLmTk());
                leadTimeAllDto.setTgNoiTinhTk(mmLmDto.getTgNoiTinhTk());
                leadTimeAllDto.setTgNoiMienTk(mmLmDto.getTgNoiMienTk());
                leadTimeAllDto.setTgLienMienTk(mmLmDto.getTgLienMienTk());
                // Kien Tiep kiem
                leadTimeAllDto.setTgTtKienTk(mmLmDto.getTgTtKienTk());
                leadTimeAllDto.setTgMmKienTk(mmLmDto.getTgMmKienTk());
                leadTimeAllDto.setTgLmKienTk(mmLmDto.getTgLmKienTk());
                leadTimeAllDto.setTgNoiTinhKienTk(mmLmDto.getTgNoiTinhKienTk());
                leadTimeAllDto.setTgNoiMienKienTk(mmLmDto.getTgNoiMienKienTk());
                leadTimeAllDto.setTgLienMienKienTk(mmLmDto.getTgLienMienKienTk());
                //Tach Kien Tiep kiem
                leadTimeAllDto.setTgTtTachKienTk(mmLmDto.getTgTtTachKienTk());
                leadTimeAllDto.setTgMmTachKienTk(mmLmDto.getTgMmTachKienTk());
                leadTimeAllDto.setTgLmTachKienTk(mmLmDto.getTgLmTachKienTk());
                leadTimeAllDto.setTgNoiTinhTachKienTk(mmLmDto.getTgNoiTinhTachKienTk());
                leadTimeAllDto.setTgNoiMienTachKienTk(mmLmDto.getTgNoiMienTachKienTk());
                leadTimeAllDto.setTgLienMienTachKienTk(mmLmDto.getTgLienMienTachKienTk());

                // Set so luong

                leadTimeAllDto.setSlDonAll(mmLmDto.getSlDonAll());
                leadTimeAllDto.setSlNoiTinhAll(mmLmDto.getSlNoiTinhAll());
                leadTimeAllDto.setSlNoiMienAll(mmLmDto.getSlNoiMienAll());
                leadTimeAllDto.setSlLienMienAll(mmLmDto.getSlLienMienAll());
                leadTimeAllDto.setSlTtNhanh(mmLmDto.getSlTtNhanh());
                leadTimeAllDto.setSlMmNhanh(mmLmDto.getSlMmNhanh());
                leadTimeAllDto.setSlLmNhanh(mmLmDto.getSlLmNhanh());
                leadTimeAllDto.setSlNoiTinhNhanh(mmLmDto.getSlNoiTinhNhanh());
                leadTimeAllDto.setSlNoiMienNhanh(mmLmDto.getSlNoiMienNhanh());
                leadTimeAllDto.setSlLienMienNhanh(mmLmDto.getSlLienMienNhanh());
                leadTimeAllDto.setSlTtTk(mmLmDto.getSlTtTk());
                leadTimeAllDto.setSlMmTk(mmLmDto.getSlMmTk());
                leadTimeAllDto.setSlLmTk(mmLmDto.getSlLmTk());
                leadTimeAllDto.setSlNoiTinhTk(mmLmDto.getSlNoiTinhTk());
                leadTimeAllDto.setSlNoiMienTk(mmLmDto.getSlNoiMienTk());
                leadTimeAllDto.setSlLienMienTk(mmLmDto.getSlLienMienTk());
                leadTimeAllDto.setSlTtKienTk(mmLmDto.getSlTtKienTk());
                leadTimeAllDto.setSlMmKienTk(mmLmDto.getSlMmKienTk());
                leadTimeAllDto.setSlLmKienTk(mmLmDto.getSlLmKienTk());
                leadTimeAllDto.setSlNoiTinhKienTk(mmLmDto.getSlNoiTinhKienTk());
                leadTimeAllDto.setSlNoiMienKienTk(mmLmDto.getSlNoiMienKienTk());
                leadTimeAllDto.setSlLienMienKienTk(mmLmDto.getSlLienMienKienTk());
                leadTimeAllDto.setSlTtTachKienTk(mmLmDto.getSlTtTachKienTk());
                leadTimeAllDto.setSlMmTachKienTk(mmLmDto.getSlMmTachKienTk());
                leadTimeAllDto.setSlLmTachKienTk(mmLmDto.getSlLmTachKienTk());
                leadTimeAllDto.setSlNoiTinhTachKienTk(mmLmDto.getSlNoiTinhTachKienTk());
                leadTimeAllDto.setSlNoiMienTachKienTk(mmLmDto.getSlNoiMienTachKienTk());
                leadTimeAllDto.setSlLienMienTachKienTk(mmLmDto.getSlLienMienTachKienTk());
                // theem 2 cot thoi gian middle mile

                leadTimeAllDto.setTgMmNoiMien(mmLmDto.getTgMmNoiMien());
                leadTimeAllDto.setTgMmLienMien(mmLmDto.getTgMmLienMien());

                // Tìm kiếm LeadtimeFmDto có cùng maChiNhanh và maBuuCuc với LeadtimeMmLmDto hiện tại

                for (LeadtimeFmDto fmDto : leadtimeFmDtos) {
                    if (fmDto != null && fmDto.getMaChiNhanh() != null && fmDto.getMaBuuCuc() != null && mmLmDto.getMaChiNhanh() != null && mmLmDto.getMaBuuCuc() != null) {
                        if (fmDto.getMaChiNhanh().equals(mmLmDto.getMaChiNhanh()) && fmDto.getMaBuuCuc().equals(mmLmDto.getMaBuuCuc())) {
                            // Gộp các giá trị từ LeadtimeFmDto vào LeadTimeAllDto
                            leadTimeAllDto.setTgFmAll(fmDto.getTgFmAll());
                            leadTimeAllDto.setTgFmNhanh(fmDto.getTgFmNhanh());
                            leadTimeAllDto.setTgFmTk(fmDto.getTgFmTk());
                            leadTimeAllDto.setTgFmKienTk(fmDto.getTgFmKienTk());
                            leadTimeAllDto.setTgFmTachKienTk(fmDto.getTgFmTachKienTk());
                            // gộp cột số lượng
                            leadTimeAllDto.setSlFmAll(fmDto.getSlFmAll());
                            leadTimeAllDto.setSlFmNhanh(fmDto.getSlFmNhanh());
                            leadTimeAllDto.setSlFmTk(fmDto.getSlFmTk());
                            leadTimeAllDto.setSlFmKienTk(fmDto.getSlFmKienTk());
                            leadTimeAllDto.setSlFmTachKienTk(fmDto.getSlFmTachKienTk());
                            break;
                        }
                    }
                }

                leadTimeAllDtos.add(leadTimeAllDto);
            }

        } catch (Exception e) {
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {} " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        List<LeadTimeAllDto> filteredList = leadTimeAllDtos.stream()
                .filter(dto -> dto.getMaChiNhanh() != null)
                .collect(Collectors.toList());


        System.out.printf("Ngay %s PROCESS SIZE %s%n",ngayBaocao,filteredList.size());
        return filteredList;
    }

    private static String getQueryKhauMmLM(String ngaybaocao) {
        String query = "select chinhanh_phat                                                                  as ma_chinhanh,\n" +
                "       buucuc_phat                                                                      as ma_buucuc,\n" +
                "\n" +
                "       AVG(TG_TT_THUCTE)                                                                as tg_tt_all,\n" +
                "       AVG(TONG_KHAU_MM)                                                                as tg_mm_all,\n" +
                "       AVG(TONG_KHAU_LM)                                                                as tg_lm_all,\n" +
                "       AVG(CASE WHEN LOAI_DON = 'NOI_TINH' THEN TG_TT_THUCTE END)                       as tg_noitinh_all,\n" +
                "       AVG(CASE WHEN LOAI_DON = 'NOI_MIEN' THEN TG_TT_THUCTE END)                       as tg_noimien_all,\n" +
                "       AVG(CASE WHEN LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN TG_TT_THUCTE END)       as tg_lienmien_all,\n" +
                "\n" +
                "\n" +
                "       AVG(CASE WHEN dich_vu = 'NHANH' THEN TG_TT_THUCTE END)                           as tg_tt_nhanh,\n" +
                "       AVG(CASE WHEN dich_vu = 'NHANH' THEN TONG_KHAU_MM END)                           as tg_mm_nhanh,\n" +
                "       AVG(CASE WHEN dich_vu = 'NHANH' THEN TONG_KHAU_LM END)                           as tg_lm_nhanh,\n" +
                "       AVG(CASE WHEN dich_vu = 'NHANH' AND LOAI_DON = 'NOI_TINH' THEN TG_TT_THUCTE END) as tg_noitinh_nhanh,\n" +
                "       AVG(CASE WHEN dich_vu = 'NHANH' AND LOAI_DON = 'NOI_MIEN' THEN TG_TT_THUCTE END) as tg_noimien_nhanh,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'NHANH' AND LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN')\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_lienmien_nhanh,\n" +
                "       AVG(CASE WHEN dich_vu = 'TIET_KIEM' THEN TG_TT_THUCTE END)                       as tg_tt_tk,\n" +
                "       AVG(CASE WHEN dich_vu = 'TIET_KIEM' THEN TONG_KHAU_MM END)                       as tg_mm_tk,\n" +
                "       AVG(CASE WHEN dich_vu = 'TIET_KIEM' THEN TONG_KHAU_LM END)                       as tg_lm_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' AND LOAI_DON = 'NOI_TINH'\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_noitinh_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' AND LOAI_DON = 'NOI_MIEN'\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_noimien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' AND LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN')\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_lienmien_tk,\n" +
                "\n" +
                "\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24)\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_tt_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24)\n" +
                "                   THEN TONG_KHAU_MM END)                                               as tg_mm_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24)\n" +
                "                   THEN TONG_KHAU_LM END)                                               as tg_lm_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) AND\n" +
                "                    LOAI_DON = 'NOI_TINH'\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_noitinh_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) AND\n" +
                "                    LOAI_DON = 'NOI_MIEN'\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_noimien_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) AND\n" +
                "                    LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN')\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_lienmien_kien_tk,\n" +
                "\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_tt_tach_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16\n" +
                "                   THEN TONG_KHAU_MM END)                                               as tg_mm_tach_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16\n" +
                "                   THEN TONG_KHAU_LM END)                                               as tg_lm_tach_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 AND LOAI_DON = 'NOI_TINH'\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_noitinh_tach_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 AND LOAI_DON = 'NOI_MIEN'\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_noimien_tach_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 AND LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN')\n" +
                "                   THEN TG_TT_THUCTE END)                                               as tg_lienmien_tach_kien_tk,\n" +
                "\n" +
                "\n" +
                "       COUNT(*)                                                                         as sl_don_all,\n" +
                "       SUM(CASE WHEN LOAI_DON = 'NOI_TINH' THEN 1 ELSE 0 END)                           as sl_noitinh_all,\n" +
                "       SUM(CASE WHEN LOAI_DON = 'NOI_MIEN' THEN 1 ELSE 0 END)                           as sl_noimien_all,\n" +
                "       SUM(CASE WHEN LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN 1 ELSE 0 END)           as sl_lienmien_all,\n" +
                "       SUM(CASE WHEN dich_vu = 'NHANH' THEN 1 ELSE 0 END)                               as sl_tt_nhanh,\n" +
                "       SUM(CASE WHEN dich_vu = 'NHANH' THEN 1 ELSE 0 END)                               as sl_mm_nhanh,\n" +
                "       SUM(CASE WHEN dich_vu = 'NHANH' THEN 1 ELSE 0 END)                               as sl_lm_nhanh,\n" +
                "       SUM(CASE WHEN dich_vu = 'NHANH' AND LOAI_DON = 'NOI_TINH' THEN 1 ELSE 0 END)     as sl_noitinh_nhanh,\n" +
                "       SUM(CASE WHEN dich_vu = 'NHANH' AND LOAI_DON = 'NOI_MIEN' THEN 1 ELSE 0 END)     as sl_noimien_nhanh,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'NHANH' AND LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_lienmien_nhanh,\n" +
                "       SUM(CASE WHEN dich_vu = 'TIET_KIEM' THEN 1 ELSE 0 END)                           as sl_tt_tk,\n" +
                "       SUM(CASE WHEN dich_vu = 'TIET_KIEM' THEN 1 ELSE 0 END)                           as sl_mm_tk,\n" +
                "       SUM(CASE WHEN dich_vu = 'TIET_KIEM' THEN 1 ELSE 0 END)                           as sl_lm_tk,\n" +
                "       SUM(CASE WHEN dich_vu = 'TIET_KIEM' AND LOAI_DON = 'NOI_TINH' THEN 1 ELSE 0 END) as sl_noitinh_tk,\n" +
                "       SUM(CASE WHEN dich_vu = 'TIET_KIEM' AND LOAI_DON = 'NOI_MIEN' THEN 1 ELSE 0 END) as sl_noimien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' AND LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_lienmien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_tt_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_mm_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_lm_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) AND\n" +
                "                    LOAI_DON = 'NOI_TINH' THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_noitinh_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) AND\n" +
                "                    LOAI_DON = 'NOI_MIEN' THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_noimien_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24) AND\n" +
                "                    LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_lienmien_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_tt_tach_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_mm_tach_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_lm_tach_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 AND LOAI_DON = 'NOI_TINH' THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_noitinh_tach_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 AND LOAI_DON = 'NOI_MIEN' THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_noimien_tach_kien_tk,\n" +
                "       SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 AND LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN 1\n" +
                "               ELSE 0 END)                                                              as sl_lienmien_tach_kien_tk,\n" +
                "       AVG(CASE WHEN LOAI_DON = 'NOI_MIEN' THEN TONG_KHAU_MM END)                                             as tg_mm_noimien, \n" +
                "       AVG(CASE WHEN LOAI_DON in ('LIEN_MIEN', 'CAN_MIEN') THEN TONG_KHAU_MM END)                             as tg_mm_lienmien "+
                "\n" +
                " from kpi_phandoan_noibo\n" +
                " where version = %s\n" +
                "  and ngay_baocao = DATE '%s'\n" +
                "  and chinhanh_phat is not null and buucuc_phat is not null" +
                " group by chinhanh_phat, buucuc_phat";

        query = String.format(query, getVersionMax(MA_CHI_SO, ngaybaocao), ngaybaocao);
        return query;

    }

    private static String getQueryLM(String ngaybaocao) {
        String query = "select chinhanh_nhan                                                                as ma_chinhanh,\n" +
                "       buucuc_nhan                                                                  as ma_buucuc,\n" +
                "       AVG(TONG_KHAU_FM)                                                            as tg_fm_all,\n" +
                "       AVG(CASE WHEN dich_vu = 'NHANH' THEN TONG_KHAU_FM END)                       as tg_fm_nhanh,\n" +
                "       AVG(CASE WHEN dich_vu = 'TIET_KIEM' THEN TONG_KHAU_FM END)                   as tg_fm_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24)\n" +
                "                   THEN TONG_KHAU_FM END)                                           as tg_fm_kien_tk,\n" +
                "       AVG(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 THEN TONG_KHAU_FM END) as tg_fm_tach_kien_tk,\n" +
                "      COUNT(*)                                                                 as sl_fm_all,\n" +
                "      SUM(CASE WHEN dich_vu = 'NHANH' THEN 1 ELSE 0 END)                       as sl_fm_nhanh,\n" +
                "      SUM(CASE WHEN dich_vu = 'TIET_KIEM' THEN 1 ELSE 0 END)                   as sl_fm_tk,\n" +
                "      SUM(CASE\n" +
                "               WHEN dich_vu = 'TIET_KIEM' and phan_loai = 'Kiện' and trong_luong > 30000 AND\n" +
                "                    (HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) > 16 AND HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) < 24)\n" +
                "                   THEN 1 ELSE 0 END)                                           as sl_fm_kien_tk,\n" +
                "     SUM(CASE\n" +
                "               WHEN dich_vu != 'TIET_KIEM' and phan_loai != 'Kiện' and trong_luong <= 30000 AND\n" +
                "                    HOUR(CAST(TG_NTC_FM AS TIMESTAMP)) <= 16 THEN 1 ELSE 0 END) as sl_fm_tach_kien_tk\n" +
                " from kpi_phandoan_noibo\n" +
                " where version = %s\n" +
                "  and ngay_baocao = DATE '%s'\n" +
                "  and chinhanh_nhan is not null and buucuc_nhan is not null" +
                " group by chinhanh_nhan, buucuc_nhan";
        query = String.format(query, getVersionMax(MA_CHI_SO, ngaybaocao), ngaybaocao);
        return query;
    }

}
