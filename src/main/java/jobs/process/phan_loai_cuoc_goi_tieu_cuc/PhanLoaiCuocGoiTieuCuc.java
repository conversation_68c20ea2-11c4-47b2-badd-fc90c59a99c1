package jobs.process.phan_loai_cuoc_goi_tieu_cuc;

import model.phanTichCuocGoiTieuCuc.PredictResponseDto;
import model.phanTichCuocGoiTieuCuc.VtpCallLogDto;
import org.codehaus.jackson.map.ObjectMapper;
import postgres.PhanLoaiCuocGoiTieuCucPostgres;
import utils.Preprocessor;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

public class Phan<PERSON>oaiCuocGoiTieuCuc extends Preprocessor {
    private ExecutorService executorService;
    private int numberAudioInRequest;
    private Semaphore semaphore;

    @Override
    public void initData() {
        super.initData();
        executorService = Executors.newFixedThreadPool(Integer.parseInt(config.getConfig("THREADPOOL_NUMBER")));
        numberAudioInRequest = Integer.parseInt(config.getConfig("REQUEST_AUDIO_NUMBER"));
        semaphore = new Semaphore(Integer.parseInt(config.getConfig("THREADPOOL_NUMBER")));
    }

    private byte[] getRecordAudio(Long startTime, String partnerCallId, String idRecord) throws IOException, ClassNotFoundException {
        String recordAudioUrl = "https://mobilecall.viettelpost.vn/svc-ticket/api/v2/accounts/de70487f42be57771f83ea02d96f4980/recordings?call-id=" + partnerCallId + "&start-time=" + startTime;
        byte[] audioBytes = new byte[0];
        try {
            URL url = new URL(recordAudioUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            // nhan res audio tu API
            BufferedInputStream inputStream = new BufferedInputStream(connection.getInputStream());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[2 * 1024 * 1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            audioBytes = outputStream.toByteArray();

            outputStream.close();
            inputStream.close();
            connection.disconnect();

        } catch (IOException e) {
            // status 0: chua xu ly
            // status 1: da xu ly
            // status 2: khong lay duoc record audio
            PhanLoaiCuocGoiTieuCucPostgres.updateProcessStatus(config, 2, idRecord);
            System.out.println("Failed to get record audio");
            e.printStackTrace();
        }
        return audioBytes;
    }

    private void writeAudioToFile(String fileName, byte[] audioBytes) throws IOException {
        FileOutputStream fileOutputStream = new FileOutputStream(fileName);
        fileOutputStream.write(audioBytes);
        fileOutputStream.close();
    }

    private void processBatch(List<VtpCallLogDto> batch) throws IOException, ClassNotFoundException {
        String boundary = "-----" + Long.toHexString(System.currentTimeMillis());
        String processingApiPredictUrl = "https://ai-voice-analytics.viettelpost.vn/v2/predict?argue-only=True";
//        String processingApiPredictUrl = "http://**************:5000/v2/predict";

        ByteArrayOutputStream requestStream = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(requestStream, "UTF-8"), true);

        List<String> createdFiles = new ArrayList<>();

        for (VtpCallLogDto vtpCallLogDto : batch) {

            String idRecord = vtpCallLogDto.getId();
            Long startTime = vtpCallLogDto.getStartTime();
            String partnerCallId = vtpCallLogDto.getPartnerCallId();
            String tempFileName = "temp_audio_" + idRecord + ".mp3";
            byte[] audioBytes = null;
            try {
                audioBytes = getRecordAudio(startTime, partnerCallId, idRecord);
                if (audioBytes == null || audioBytes.length == 0) {
                    continue;
                }
                writeAudioToFile(tempFileName, audioBytes);

                createdFiles.add(tempFileName);

                writer.append("--").append(boundary).append("\r\n");
                writer.append("Content-Disposition: form-data; name=\"files\"; filename=\"" + tempFileName + "\"").append("\r\n");
                writer.append("Content-Type: audio/mpeg").append("\r\n");
                writer.append("\r\n").flush();
                requestStream.write(audioBytes);
                requestStream.flush();
                writer.append("\r\n").flush();
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }

        writer.append("--").append(boundary).append("--").append("\r\n");
        writer.close();

        byte[] requestData = requestStream.toByteArray();
        if (createdFiles.size() > 0) {
            try {
                URL url = new URL(processingApiPredictUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
                connection.setDoOutput(true);
                connection.setConnectTimeout(30000);

                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(requestData);
                outputStream.close();
                for (String fileName : createdFiles) {
                    System.out.println("List audio predict: " + fileName);

                }

                int responseCode = connection.getResponseCode();

                if (responseCode == HttpURLConnection.HTTP_OK) {

                    InputStream inputStream = connection.getInputStream();
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = bufferedReader.readLine()) != null) {
                        response.append(line);
                    }
                    bufferedReader.close();

                    ObjectMapper objectMapper = new ObjectMapper();
                    HashMap<String, LinkedHashMap<String, Boolean>> processingResponse = objectMapper.readValue(response.toString(), HashMap.class);

                    for (Map.Entry<String, LinkedHashMap<String, Boolean>> entry : processingResponse.entrySet()) {
                        String fileName = entry.getKey();

                        int underscoreIndex = fileName.lastIndexOf("_");
                        int dotIndex = fileName.lastIndexOf(".");
                        String idRecord = fileName.substring(underscoreIndex + 1, dotIndex);
                        LinkedHashMap<String, Boolean> data = entry.getValue();

                        PredictResponseDto predictResponseDto = new PredictResponseDto();
                        if (data.get("contain_speech") == true && data.get("has_valid_length") == true) {
                            if (data.get("offensive") == null || data.get("honorifics") == null) {
                                // status 4: offensive = null, honorifics = null, hoi thoai binh thuong
                                PhanLoaiCuocGoiTieuCucPostgres.updateProcessStatus(config, 4, idRecord);
                            } else {
                                predictResponseDto.setThoTuc(data.get("offensive"));
                                predictResponseDto.setXungHo(data.get("honorifics"));
                                predictResponseDto.setTranhCai(data.get("argue"));

                                PhanLoaiCuocGoiTieuCucPostgres.update(config, predictResponseDto.isThoTuc() ? 1 : 0, predictResponseDto.isXungHo() ? 1 : 0, 1, predictResponseDto.isTranhCai(), idRecord);
                            }

                        } else {
                            // status 0: chua xu ly
                            // status 1: da xu ly
                            // status 2: khong lay duoc record audio
                            // status 3: contain_speech = false, has_valid_length = false
                            PhanLoaiCuocGoiTieuCucPostgres.updateProcessStatus(config, 3, idRecord);
                        }

                    }

                } else {
                    System.err.println("Failed to connect to audio processing API. Response code: " + responseCode);
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("Error processing AI response: " + e.getMessage());

            } finally {
                for (String fileName : createdFiles) {
                    File fileToDelete = new File(fileName);
                    if (fileToDelete.exists()) {
                        fileToDelete.delete();
                    }
                }
            }
        }
    }

    @Override
    public void process() {
        List<VtpCallLogDto> listParamsRecord = PhanLoaiCuocGoiTieuCucPostgres.getListStartTimeAndPartnerCallId(config);
        boolean interrupted = false;

        for (int i = 0; i < listParamsRecord.size(); i += numberAudioInRequest) {
            List<VtpCallLogDto> batch = listParamsRecord.subList(i, Math.min(i + numberAudioInRequest, listParamsRecord.size()));
            try {
                semaphore.acquire();
                if (interrupted) {
                    continue;
                }
                executorService.submit(() -> {
                    try {
                        processBatch(batch);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        semaphore.release();
                    }
                });
            } catch (InterruptedException e) {
                interrupted = true;
                e.printStackTrace();
                break;
            }
        }

        executorService.shutdown();
        try {
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
