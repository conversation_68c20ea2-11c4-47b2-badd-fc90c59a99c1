package jobs.process.leadtime;

import jobs.common.leadtime.LeadTimeLuyKe;
import jobs.common.leadtime.LeadTimeNgay;
import model.leadtime.LeadTimeAllDto;
import model.leadtime.LeadTimeLkThang;
import postgres.leadtime.LeadTimePostgres;
import utils.Preprocessor;

import java.time.LocalDate;
import java.util.List;

public class LeadTimeProcess extends Preprocessor {

    @Override
    public void initData() {
        super.initData();
    }

    @Override
    public void process() {
        if (isLuyKe == 0) {
            // check ngày xem nếu ngày dateStart thì chạy ngày hiện tại, không sẽ chạy lại ngày quá khứ chỉ định
            if (dateStart.equals("null")) {
                dateStart = LocalDate.now().minusDays(1).toString();
                System.out.println("DATE : " + dateStart);
                insertLeadTimeNgay(dateStart);
            } else {
                for (int i = 0; i < numDays; i++) {
                    String currentDate = LocalDate.parse(dateStart).minusDays(i).toString();
                    System.out.println("DATE : " + currentDate);
                    insertLeadTimeNgay(currentDate);
                }
            }
        } else {
            // check ngày xem nếu ngày dateStart thì chạy ngày hiện tại, không sẽ chạy lại ngày quá khứ chỉ định
            if (dateStart.equals("null")) {
                dateStart = LocalDate.now().minusDays(1).toString();
                System.out.println("DATE : " + dateStart);
                insertLeadTimeLuyKe(dateStart);
            } else {
                // xu lý chạy lũy kế khi đã chạy xong postgres
                for (int i = 0; i <= numDays; i++) {
                    String currentDate = LocalDate.parse(dateStartLK).minusDays(i).toString();
                    System.out.println("DATE : " + currentDate);
                    insertLeadTimeLuyKe(currentDate);
                }
            }
        }
    }

    //Thực hiện insert lũy kế
    private static void insertLeadTimeLuyKe(String dateStart) {
        String firstDayOfMonth = LocalDate.parse(dateStart).withDayOfMonth(1).toString();
        List<LeadTimeLkThang> mList = LeadTimeLuyKe.getLuyKeLeadTime(config, firstDayOfMonth, dateStart);
        for (LeadTimeLkThang leadTime : mList) {
            leadTime.setNgayBaoCao(dateStart);
        }
        LeadTimePostgres.insertLuyKe(config, mList);
    }

    // thực hiện insert ngày
    private static void insertLeadTimeNgay(String dateStart) {
        List<LeadTimeAllDto> leadTimeAllDtoList = LeadTimeNgay.calculateLeadTimeDate(dateStart);
        LeadTimePostgres.insertNgay(config, leadTimeAllDtoList);
    }
}
