package jobs.process.cldv_topcn;

import jobs.common.cldv_topcn.TopChinhanhCommon;
import model.cldv_cn.TopModel;
import postgres.TopCLDVPostgres;
import utils.Preprocessor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class TopChiNhanhProcess extends Preprocessor {

    private TopChinhanhCommon topChinhanhCommon;
    private List<TopModel> topModelList;

    @Override
    public void initData() {
        super.initData();
        topChinhanhCommon = new TopChinhanhCommon();
        topModelList = new ArrayList<>();
    }

    @Override
    public void process() {
        if (dateStart.equals("null")) {
            dateStart = LocalDate.now().minusDays(1).toString();
            System.out.println("DATE : " + dateStart);
            insertTopNgay(dateStart);
        } else {
            for (int i = 0; i < numDays; i++) {
                String currentDate = LocalDate.parse(dateStart).minusDays(i).toString();
                System.out.println("DATE : " + currentDate);
                insertTopNgay(currentDate);
            }
        }
    }

    // thực hiện insert ngày
    private void insertTopNgay(String dateStart) {
        topModelList = topChinhanhCommon.calculateData(config, dateStart);
        TopCLDVPostgres.insert(config, topModelList);
    }
}
