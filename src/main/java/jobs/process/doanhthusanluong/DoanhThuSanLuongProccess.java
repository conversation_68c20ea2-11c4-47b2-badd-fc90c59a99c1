package jobs.process.doanhthusanluong;

import jobs.common.doanhthusanluong.DoanhThuSanLuongCommon;
import model.doanhthusanluong.DoanhThuSanLuong;
import postgres.doanhthusanluong.DoanhThuSanLuongPostgres;
import postgres.smsdoanhthu.SMSDoanhThuPostgres;
import utils.Preprocessor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class DoanhThuSanLuongProccess extends Preprocessor {
    private DoanhThuSanLuongCommon doanhThuSanLuongCommon;
    private List<DoanhThuSanLuong> doanhThuSanLuongList;

    @Override
    public void initData() {
        super.initData();
        doanhThuSanLuongCommon = new DoanhThuSanLuongCommon();
        doanhThuSanLuongList = new ArrayList<>();
    }

    @Override
    public void process() throws ClassNotFoundException {
        if (dateStart.equals("null")) {
            dateStart = LocalDate.now().minusDays(1).toString();
            System.out.println("DATE : " + dateStart);
            insertSMSDoanhThuRecord(dateStart);
        } else {
            for (int i = 0; i < numDays; i++) {
                String currentDate = LocalDate.parse(dateStart).minusDays(i).toString();
                System.out.println("DATE : " + currentDate);
                insertSMSDoanhThuRecord(currentDate);
            }
        }
    }

    private void insertSMSDoanhThuRecord(String dateStart) throws ClassNotFoundException {
        doanhThuSanLuongList = doanhThuSanLuongCommon.calculateData(dateStart);
        DoanhThuSanLuongPostgres.insert(config, doanhThuSanLuongList);
    }
}
