package jobs.process.phat_hien_don_gach_ao;

import model.phanHienDonGachAo.OrderImageDto;
import model.phanHienDonGachAo.PredictImageResponseDto;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONObject;
import postgres.PhatHienDonGachAoPostgres;
import utils.MultiPartBodyPublisher;
import utils.Preprocessor;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.ProxySelector;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

public class PhatHienDonGachAo extends Preprocessor {
    private ExecutorService executorService;
    private int numberImgInRequest;
    private Semaphore semaphore;
    private int maxRecords;
    private boolean isOnlyProcessDataDonGachAoN1;

    @Override
    public void initData() {
        super.initData();
        executorService = Executors.newFixedThreadPool(Integer.parseInt(config.getConfig("THREADPOOL_NUMBER")));
        numberImgInRequest = Integer.parseInt(config.getConfig("REQUEST_IMG_NUMBER_DON_GACH_AO"));
        semaphore = new Semaphore(Integer.parseInt(config.getConfig("THREADPOOL_NUMBER_DON_GACH_AO")));
        maxRecords = Integer.parseInt(config.getConfig("MAX_RECORDS_DON_GACH_AO_PROCESS"));
        isOnlyProcessDataDonGachAoN1 = Boolean.parseBoolean(config.getConfig("ONLY_PROCESS_DATA_DON_GACH_AO_N_1"));
    }

    private static byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] temp = new byte[1024 * 8192];
        int bytesRead;
        while ((bytesRead = inputStream.read(temp)) != -1) {
            buffer.write(temp, 0, bytesRead);
        }
        return buffer.toByteArray();
    }

    private static byte[] getImage(String imgLink, String idImg) throws IOException, InterruptedException, ClassNotFoundException {
        byte[] imageBytes = new byte[0];

        HttpClient httpClient = HttpClient.newHttpClient();
        // add proxy for request from local
//        HttpClient httpClient = HttpClient.newBuilder().proxy(ProxySelector.of(InetSocketAddress.createUnresolved("*************", 8085))).build();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(imgLink))
                .timeout(Duration.ofSeconds(30))
                .build();
        try {
            HttpResponse<?> response = httpClient.send(request, HttpResponse.BodyHandlers.ofByteArray());
            int statusCode = response.statusCode();

            if (statusCode == 200) {
                imageBytes = (byte[]) response.body();
            } else {
                throw new IOException("HTTP response code: " + statusCode);
            }
        } catch (IOException e) {
            PhatHienDonGachAoPostgres.updateProcessStatus(config, 2, idImg);
            System.err.println("Failed to get image: " + e.getMessage() + idImg + "_link: " + imgLink);
        }
        return imageBytes;
    }

    public static int levenshteinDistance(String a, String b) {
        int[][] dp = new int[a.length() + 1][b.length() + 1];

        for (int i = 0; i <= a.length(); i++) {
            for (int j = 0; j <= b.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else {
                    int cost = (a.charAt(i - 1) == b.charAt(j - 1)) ? 0 : 1;
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1), dp[i - 1][j - 1] + cost);
                }
            }
        }

        return dp[a.length()][b.length()];
    }

    public static float calculateSimilarity(String a, String b) {
        int distance = levenshteinDistance(a, b);
        int maxLength = Math.max(a.length(), b.length());

        // Calculate similarity ratio
        float similarity = ((float) (maxLength - distance) / maxLength) * 100;
        return similarity;
    }

    private String getIdImageFromFileName(List<OrderImageDto> batch, String fileName) {
        for (OrderImageDto dto : batch) {
            if (dto.getId().concat(".jpg").equals(fileName)) {
                return dto.getId();
            }
        }
        return null;
    }

    private String getMaPhieuGuiFromBatch(List<OrderImageDto> batch, String idImage) {
        for (OrderImageDto dto : batch) {
            if (dto.getId().equals(idImage)) {
                return dto.getMa_phieugui();
            }
        }
        return null;
    }

    private void processBatch(List<OrderImageDto> batch) throws IOException, ClassNotFoundException, InterruptedException {
        String boundary = "-----" + Long.toHexString(System.currentTimeMillis());
        String processingApiPredictUrl = "https://ai-image-fraud.viettelpost.vn";
//        String processingApiPredictUrl = "http://**************:5000";

        HttpClient httpClient = HttpClient.newHttpClient();
//        HttpClient httpClient = HttpClient.newBuilder().proxy(ProxySelector.of(InetSocketAddress.createUnresolved("*************", 8080))).build();

        MultiPartBodyPublisher multipartBodyPublisher = new MultiPartBodyPublisher();
        for (OrderImageDto dto : batch) {
            String idImage = dto.getId();
            String imageLink = dto.getImgLink();
            byte[] imageBytes = getImage(imageLink, idImage);


            String contentType = Files.probeContentType(Paths.get(imageLink));
            multipartBodyPublisher.addPart("image_list", imageBytes, idImage + ".jpg", contentType);
        }
        try {

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(new URI(processingApiPredictUrl))
                    .timeout(Duration.ofSeconds(30))
                    .header("Content-Type", "multipart/form-data; boundary=" + multipartBodyPublisher.getBoundary())
                    .POST(multipartBodyPublisher.build())
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            System.out.println("Response status code: " + response.statusCode());
            if (response.statusCode() == 200) {
                JSONObject jsonResponse = new JSONObject(response.body());
                JSONArray dataArray = jsonResponse.getJSONArray("data");

                for (int i = 0; i < dataArray.length(); i++) {
                    JSONObject jsonObject = dataArray.getJSONObject(i);
                    String fileName = (String) jsonObject.keys().next();
                    JSONObject dataObject = jsonObject.getJSONObject(fileName);
                    String idImage = getIdImageFromFileName(batch, fileName);

                    if (idImage != null) {
                        PredictImageResponseDto predictImageResponseDto = new PredictImageResponseDto();
                        predictImageResponseDto.setOrderCode(dataObject.getString("order_code"));
                        predictImageResponseDto.setOrderCodeProb(dataObject.optString("order_code_prob"));
                        predictImageResponseDto.setBarCode(dataObject.getString("bar_code"));
                        predictImageResponseDto.setBarCodeProb(dataObject.optString("bar_code_prob"));
                        predictImageResponseDto.setQrCode(dataObject.getString("qr_code"));
                        predictImageResponseDto.setQrCodeProb(dataObject.optString("qr_code_prob"));
                        predictImageResponseDto.setSignature(dataObject.getString("signature"));
                        predictImageResponseDto.setSignatureProb(dataObject.optString("signature_prob"));

                        String maPhieuGui = getMaPhieuGuiFromBatch(batch, idImage);
                        Float similarity = calculateSimilarity(maPhieuGui, predictImageResponseDto.getOrderCode());
                        System.out.println("idImage Process success: " + idImage);
                        System.out.println("ma_phieugui_dudoan " + predictImageResponseDto.getOrderCode());

                        // status 0: chua xu ly
                        // status 1: da xu ly
                        // status 2: khong lay duoc image
                        PhatHienDonGachAoPostgres.update(config, 1, predictImageResponseDto.getQrCode().equals("YES") ? true : false,
                                predictImageResponseDto.getBarCode().equals("YES") ? true : false,
                                predictImageResponseDto.getSignature().equals("YES") ? true : false,
                                predictImageResponseDto.getOrderCode(), similarity, idImage);
                    }
                }

            } else {
                System.err.println("Failed to connect to image processing API. Response code: " + response.statusCode());
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error processing AI response: " + e.getMessage());

        }
    }

    @Override
    public void process() throws ClassNotFoundException {
        List<OrderImageDto> listImages = PhatHienDonGachAoPostgres.getListImgLinkAndId(config, isOnlyProcessDataDonGachAoN1);
        boolean interrupted = false;

        if (listImages.size() > maxRecords) {
            listImages = listImages.subList(0, maxRecords);
        }

        for (int i = 0; i < listImages.size(); i += numberImgInRequest) {
            List<OrderImageDto> batch = listImages.subList(i, Math.min(i + numberImgInRequest, listImages.size()));
            try {
                semaphore.acquire();
                if (interrupted) {
                    continue;
                }
                executorService.submit(() -> {
                    try {
                        processBatch(batch);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        semaphore.release();
                    }
                });
            } catch (InterruptedException e) {
                interrupted = true;
                e.printStackTrace();
                break;
            }
        }

        executorService.shutdown();
        try {
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
