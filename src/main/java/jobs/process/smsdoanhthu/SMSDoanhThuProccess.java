package jobs.process.smsdoanhthu;

import jobs.common.smsdoanhthu.SMSDoanhThuCommon;
import model.smsdoanhthu.SMSDoanhThu;
import postgres.smsdoanhthu.SMSDoanhThuPostgres;
import utils.Preprocessor;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class SMSDoanhThuProccess extends Preprocessor {
    private SMSDoanhThuCommon smsDoanhThuCommon;
    private List<SMSDoanhThu> smsDoanhThuList;

    @Override
    public void initData() {
        super.initData();
        smsDoanhThuCommon = new SMSDoanhThuCommon();
        smsDoanhThuList = new ArrayList<>();
    }

    @Override
    public void process() throws ClassNotFoundException, IOException {
        if (dateStart.equals("null")) {
            dateStart = LocalDate.now().minusDays(1).toString();
            System.out.println("DATE : " + dateStart);
            insertSMSDoanhThuRecord(dateStart);
        } else {
            for (int i = 0; i < numDays; i++) {
                String currentDate = LocalDate.parse(dateStart).minusDays(i).toString();
                System.out.println("DATE : " + currentDate);
                insertSMSDoanhThuRecord(currentDate);
            }
        }
    }

    private void insertSMSDoanhThuRecord(String dateStart) throws ClassNotFoundException, IOException {
        smsDoanhThuList = smsDoanhThuCommon.calculateData(config, dateStart);
        SMSDoanhThuPostgres.insert(config, smsDoanhThuList);
    }
}
