package utils;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class MultiPartBodyPublisher {
    private static final String BOUNDARY = UUID.randomUUID().toString();
    private static final String LINE_BREAK = "\r\n";
    private List<byte[]> bodyParts = new ArrayList<>();

    public MultiPartBodyPublisher addPart(String name, String value) {
        bodyParts.add(("--" + BOUNDARY + LINE_BREAK).getBytes());
        bodyParts.add(("Content-Disposition: form-data; name=\"" + name + "\"" + LINE_BREAK + LINE_BREAK).getBytes());
        bodyParts.add((value + LINE_BREAK).getBytes());
        return this;
    }

    public MultiPartBodyPublisher addPart(String name, byte[] fileBytes, String fileName, String contentType) {
        bodyParts.add(("--" + BOUNDARY + LINE_BREAK).getBytes());
        bodyParts.add(("Content-Disposition: form-data; name=\"" + name + "\"; filename=\"" + fileName + "\"" + LINE_BREAK).getBytes());
        bodyParts.add(("Content-Type: " + contentType + LINE_BREAK + LINE_BREAK).getBytes());
        bodyParts.add(fileBytes);
        bodyParts.add(LINE_BREAK.getBytes());
        return this;
    }

    public HttpRequest.BodyPublisher build() {
        bodyParts.add(("--" + BOUNDARY + "--" + LINE_BREAK).getBytes());
        return HttpRequest.BodyPublishers.ofByteArrays(bodyParts);
    }

    public String getBoundary() {
        return BOUNDARY;
    }
}
