package utils;

import configs.Configuration;

import java.io.IOException;

import static configs.ConfigurationFactory.getConfigInstance;

public abstract class Preprocessor {
    public static Configuration config;
    public int isLuyKe;
    public int numDays;
    public String dateStart;
    public String dateStartLK;

    public abstract void process() throws ClassNotFoundException, IOException;

    public void initData() {
        config = getConfigInstance();
        dateStart = config.getConfig("TIMESTART");
        dateStartLK = config.getConfig("TIMESTART");
        numDays = Integer.parseInt(config.getConfig("NUM_DAYS"));
        isLuyKe = Integer.parseInt(config.getConfig("IS_LUYKE"));

        System.out.println("TIMESTART : " + dateStart);
        System.out.println("NUM_DAYS : " + numDays);
        System.out.println("MODE : " + isLuyKe);
    }
}
