package utils;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.TimeZone;

public class Utils {
    public static float round(float number) {
        DecimalFormat df = new DecimalFormat("#.##");
        return Float.parseFloat(df.format(number));
    }

    public static double roundDoubleNum(double number) {
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return Double.parseDouble(df.format(number));
    }
    public static String convertLongTimestampToString(Long timestamp) {
        if (timestamp != null) {
            Instant instant = Instant.ofEpochMilli(timestamp); // Chuyển đổi thành mili giây
            Date date = Date.from(instant);

            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
            return outputFormat.format(date);
        }
        return null;
    }

}
