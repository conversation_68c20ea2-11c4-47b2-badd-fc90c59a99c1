package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateTimeUtil {

    private static final Logger logger = LoggerFactory.getLogger(DateTimeUtil.class);


    public static String getDateFormatYYYYMM(String ngay){
        // Tách chuỗi thành năm, tháng và ngày
        String[] parts = ngay.split("-");
        String nam = parts[0];
        String thang = parts[1];

//        // Xoá kí tự 0 ở đầu nếu có
//        if (thang.startsWith("0")) {
//            thang = thang.substring(1);
//        }

        return nam + thang;
    }

    public static LocalDate getFirstDay(String date) {
        String[] splitDate = date.split("-");
        LocalDate initial = LocalDate.of(Integer.parseInt(splitDate[0]), Integer.parseInt(splitDate[1]), Integer.parseInt(splitDate[2]));
        LocalDate firstDate = initial.withDayOfMonth(1);
        LocalDate endDate = initial.withDayOfMonth(initial.getMonth().length(initial.isLeapYear()));
        return firstDate;
    }

    public static long dateToEpochTimeStampStartDate(LocalDate date) {
        LocalDateTime batDauNgay = date.atStartOfDay();
        Instant instant = batDauNgay.atZone(ZoneId.systemDefault()).toInstant();
        Date dateConvert = Date.from(instant);
        return dateConvert.getTime();
    }


    public static String BigIntToTimestampToStringKhaoSatDG(String bigintValue) {
        if(bigintValue!=null){
            if(Long.valueOf(bigintValue)==0){
                return null;
            }else {
                long timestamp = Long.parseLong(bigintValue); // Giả sử timestamp của bạn
                Instant instant = Instant.ofEpochMilli(timestamp);
                LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd-yyyy HH:mm:ss");
                String formattedDateTime = formatter.format(localDateTime);
                return formattedDateTime;
            }
        }else {
            return null;
        }
    }


}
