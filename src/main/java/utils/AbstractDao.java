package utils;
import configs.Configuration;
import connection.PhoenixConnectionFactory;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


public abstract class AbstractDao {

//    protected static final Logger eLogger = LogManager.getLogger("ErrorLog");
//    protected static final Logger cLogger = LogManager.getLogger("CacheLog");
    protected static void releaseConnect(Connection conn, PreparedStatement stmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException ex) {
            }
        }
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException ex) {
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException ex) {
            }
        }
    }
    public static long getVersionMax(String loaiBC, String ngayBaocao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlMax = String.format("SELECT max(version) as version from chiso_version where ma_chiso = '%s' and ngay_baocao = date '%s'",loaiBC,ngayBaocao);
        long maxVersion = 0L;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            while (rs.next()) {
                maxVersion = rs.getLong("version");
            }
        } catch (Exception e) {
//            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}" + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return maxVersion;
    }
}

