package postgres.doanhthusanluong;

import configs.Configuration;
import model.doanhthusanluong.DoanhThuSanLuong;

import java.sql.*;
import java.util.List;

public class DoanhThuSanLuongPostgres {
    public static void insert(Configuration config, List<DoanhThuSanLuong> doanhThuSanLuongList) {
        String queryTable = "INSERT INTO doanhthu_sanluong (ngay_baocao, ma_cn, ma_buucuc, " +
                "dt_cptn, dt_vt, dt_exp, dt_kho, tong_dt_lk_thang, " +
                "sl_cptn, sl_vt, sl_exp, sl_kho, tong_sl_lk_thang, " +
                "updated_at) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                "on conflict (ngay_baocao, ma_cn, ma_buucuc) do update set " +
                "dt_cptn = excluded.dt_cptn, dt_vt = excluded.dt_vt, dt_exp = excluded.dt_exp, dt_kho = excluded.dt_kho, tong_dt_lk_thang = excluded.tong_dt_lk_thang, " +
                "sl_cptn = excluded.sl_cptn, sl_vt = excluded.sl_vt, sl_exp = excluded.sl_exp, sl_kho = excluded.sl_kho, tong_sl_lk_thang = excluded.tong_sl_lk_thang, " +
                "updated_at = excluded.updated_at";
        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_QUALITY"), config.getConfig("POSTGRES.USER_QUALITY"), config.getConfig("POSTGRES.PASSWD_QUALITY"))) {
            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(queryTable)) {
                for (DoanhThuSanLuong doanhThuSanLuong : doanhThuSanLuongList) {
                    pstmt.setDate(1, Date.valueOf(doanhThuSanLuong.getNgayBaoCao()));
                    pstmt.setString(2, doanhThuSanLuong.getMaChiNhanh());
                    pstmt.setString(3, doanhThuSanLuong.getMaBuuCuc());
                    pstmt.setDouble(4, doanhThuSanLuong.getDoanhThuCPTN());
                    pstmt.setDouble(5, doanhThuSanLuong.getDoanhThuVT());
                    pstmt.setDouble(6, doanhThuSanLuong.getDoanhThuEXP());
                    pstmt.setDouble(7, doanhThuSanLuong.getDoanhThuKho());
                    pstmt.setDouble(8, doanhThuSanLuong.getDoanhThuThang());
                    pstmt.setLong(9, doanhThuSanLuong.getSanLuongCPTN());
                    pstmt.setLong(10, doanhThuSanLuong.getSanLuongVT());
                    pstmt.setLong(11, doanhThuSanLuong.getSanLuongEXP());
                    pstmt.setLong(12, doanhThuSanLuong.getSanLuongKho());
                    pstmt.setLong(13, doanhThuSanLuong.getSanLuongThang());
                    pstmt.setLong(14, doanhThuSanLuong.getUpdateTime());
                    pstmt.addBatch();
                }
                pstmt.executeBatch();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("SQLException" + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException e) {
                    System.out.println("SQLException" + e.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }

    }
}
