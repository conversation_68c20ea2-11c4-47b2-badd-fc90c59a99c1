package postgres;

import configs.Configuration;
import model.phanTichCuocGoiTieuCuc.VtpCallLogDto;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class PhanLoaiCuocGoiTieuCucPostgres {

    public static void update(Configuration config, int thoTuc, int xungHo, int processStatus, Boolean tranhCai, String id) throws ClassNotFoundException {
        String update = "update vtp_call_log set tho_tuc = ?, xung_ho = ? , process_status = ? , tranh_cai = ?  where id = ?";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {

            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(update)) {
                pstmt.setInt(1, thoTuc);
                pstmt.setInt(2, xungHo);
                pstmt.setInt(3, processStatus);
                pstmt.setBoolean(4, tranhCai);
                pstmt.setString(5, id);


                pstmt.executeUpdate();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("Error updating row: " + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException rollbackEx) {
                    System.out.println("Rollback failed: " + rollbackEx.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }

    }

    public static void updateProcessStatus(Configuration config, int processStatus, String id) throws ClassNotFoundException {
        String update = " update vtp_call_log set process_status = ? where id = ? ";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(update)) {
                pstmt.setInt(1, processStatus);
                pstmt.setString(2, id);

                pstmt.executeUpdate();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("Error updating process status: " + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException rollbackEx) {
                    System.out.println("Rollback failed: " + rollbackEx.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }

    }

    public static List<VtpCallLogDto> getListStartTimeAndPartnerCallId(Configuration config) {
        String query = String.format("select id, start_time, partner_call_id from vtp_call_log  where process_status = 0 and  start_time >= %s", config.getConfig("TIMESTAMPSTART"));
        List<VtpCallLogDto> listRes = new ArrayList<>();
//        connect db local
//        Class.forName("org.postgresql.Driver");
//        String conn = "*****************************************";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {
//        try (Connection connection = DriverManager.getConnection(conn, "postgres", "postgres") ){
            connection.setAutoCommit(false);
            try (PreparedStatement preparedStatement = connection.prepareStatement(query)) {
                try (ResultSet resultSet = preparedStatement.executeQuery()) {

                    while (resultSet.next()) {
                        VtpCallLogDto dto = new VtpCallLogDto();
                        dto.setId(resultSet.getString("id"));
                        dto.setStartTime(resultSet.getLong("start_time"));
                        dto.setPartnerCallId(resultSet.getString("partner_call_id"));
                        dto.setStartTime(resultSet.getLong("start_time"));

                        listRes.add(dto);
                    }
                }
            }
        } catch (SQLException e) {
            System.out.println("SQLException" + e.getMessage());
            e.printStackTrace();

        }
        return listRes;
    }
}
