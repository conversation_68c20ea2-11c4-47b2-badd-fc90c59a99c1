package postgres.leadtime;

import configs.Configuration;
import model.leadtime.LeadTimeAllDto;
import model.leadtime.LeadTimeLkThang;

import java.sql.*;
import java.util.List;

public class LeadTimePostgres {

    public static void insertNgay(Configuration config, List<LeadTimeAllDto> dtoList) {
        String queryTable = "INSERT INTO " +
                "dash_tct_leadtime_ngay (" +
                "ngay_baocao," +
                " ma_chinhanh," +
                " ma_buucuc," +
                " tg_tt_all," +
                " tg_fm_all," +
                " tg_mm_all," +
                " tg_lm_all," +
                " tg_noitinh_all," +
                " tg_noimien_all," +
                " tg_lienmien_all," +
                " tg_tt_nhanh," +
                " tg_fm_nhanh," +
                " tg_mm_nhanh," +
                " tg_lm_nhanh," +
                " tg_noitinh_nhanh," +
                " tg_noimien_nhanh," +
                " tg_lienmien_nhanh," +
                " tg_tt_tk," +
                " tg_fm_tk," +
                " tg_mm_tk," +
                " tg_lm_tk," +
                " tg_noitinh_tk," +
                " tg_noimien_tk," +
                " tg_lienmien_tk," +
                " tg_tt_kien_tk," +
                " tg_fm_kien_tk," +
                " tg_mm_kien_tk," +
                " tg_lm_kien_tk," +
                " tg_noitinh_kien_tk," +
                " tg_noimien_kien_tk," +
                " tg_lienmien_kien_tk," +
                " tg_tt_tach_kien_tk," +
                " tg_fm_tach_kien_tk," +
                " tg_mm_tach_kien_tk," +
                " tg_lm_tach_kien_tk," +
                " tg_noitinh_tach_kien_tk," +
                " tg_noimien_tach_kien_tk," +
                " tg_lienmien_tach_kien_tk," +
                " sl_don_all," +
                " sl_noitinh_all," +
                " sl_noimien_all," +
                " sl_lienmien_all," +
                " sl_tt_nhanh," +
                " sl_mm_nhanh," +
                " sl_lm_nhanh," +
                " sl_noitinh_nhanh," +
                " sl_noimien_nhanh," +
                " sl_lienmien_nhanh," +
                " sl_tt_tk, sl_mm_tk," +
                " sl_lm_tk," +
                " sl_noitinh_tk," +
                " sl_noimien_tk," +
                " sl_lienmien_tk," +
                " sl_tt_kien_tk," +
                " sl_mm_kien_tk," +
                " sl_lm_kien_tk," +
                " sl_noitinh_kien_tk," +
                " sl_noimien_kien_tk," +
                " sl_lienmien_kien_tk," +
                " sl_tt_tach_kien_tk," +
                " sl_mm_tach_kien_tk," +
                " sl_lm_tach_kien_tk," +
                " sl_noitinh_tach_kien_tk," +
                " sl_noimien_tach_kien_tk," +
                " sl_lienmien_tach_kien_tk," +
                " sl_fm_all," +
                " sl_fm_nhanh," +
                " sl_fm_tk," +
                " sl_fm_kien_tk," +
                " sl_fm_tach_kien_tk,tg_mm_noimien,tg_mm_lienmien) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)" +
                "ON CONFLICT (ngay_baocao, ma_chinhanh, ma_buucuc) DO UPDATE " +
                "SET tg_tt_all = EXCLUDED.tg_tt_all, tg_fm_all = EXCLUDED.tg_fm_all, tg_mm_all = EXCLUDED.tg_mm_all, " +
                "tg_lm_all = EXCLUDED.tg_lm_all, tg_noitinh_all = EXCLUDED.tg_noitinh_all, tg_noimien_all = EXCLUDED.tg_noimien_all, " +
                "tg_lienmien_all = EXCLUDED.tg_lienmien_all, tg_tt_nhanh = EXCLUDED.tg_tt_nhanh, tg_fm_nhanh = EXCLUDED.tg_fm_nhanh, " +
                "tg_mm_nhanh = EXCLUDED.tg_mm_nhanh, tg_lm_nhanh = EXCLUDED.tg_lm_nhanh, tg_noitinh_nhanh = EXCLUDED.tg_noitinh_nhanh, " +
                "tg_noimien_nhanh = EXCLUDED.tg_noimien_nhanh, tg_lienmien_nhanh = EXCLUDED.tg_lienmien_nhanh, tg_tt_tk = EXCLUDED.tg_tt_tk, " +
                "tg_fm_tk = EXCLUDED.tg_fm_tk, tg_mm_tk = EXCLUDED.tg_mm_tk, tg_lm_tk = EXCLUDED.tg_lm_tk, tg_noitinh_tk = EXCLUDED.tg_noitinh_tk, " +
                "tg_noimien_tk = EXCLUDED.tg_noimien_tk, tg_lienmien_tk = EXCLUDED.tg_lienmien_tk, tg_tt_kien_tk = EXCLUDED.tg_tt_kien_tk, " +
                "tg_fm_kien_tk = EXCLUDED.tg_fm_kien_tk, tg_mm_kien_tk = EXCLUDED.tg_mm_kien_tk, tg_lm_kien_tk = EXCLUDED.tg_lm_kien_tk, " +
                "tg_noitinh_kien_tk = EXCLUDED.tg_noitinh_kien_tk, tg_noimien_kien_tk = EXCLUDED.tg_noimien_kien_tk, " +
                "tg_lienmien_kien_tk = EXCLUDED.tg_lienmien_kien_tk, tg_tt_tach_kien_tk = EXCLUDED.tg_tt_tach_kien_tk, " +
                "tg_fm_tach_kien_tk = EXCLUDED.tg_fm_tach_kien_tk, tg_mm_tach_kien_tk = EXCLUDED.tg_mm_tach_kien_tk, " +
                "tg_lm_tach_kien_tk = EXCLUDED.tg_lm_tach_kien_tk, tg_noitinh_tach_kien_tk = EXCLUDED.tg_noitinh_tach_kien_tk, " +
                "tg_noimien_tach_kien_tk = EXCLUDED.tg_noimien_tach_kien_tk, tg_lienmien_tach_kien_tk = EXCLUDED.tg_lienmien_tach_kien_tk, " +
                "sl_don_all = EXCLUDED.sl_don_all, sl_noitinh_all = EXCLUDED.sl_noitinh_all, sl_noimien_all = EXCLUDED.sl_noimien_all, " +
                "sl_lienmien_all = EXCLUDED.sl_lienmien_all, sl_tt_nhanh = EXCLUDED.sl_tt_nhanh, sl_mm_nhanh = EXCLUDED.sl_mm_nhanh, " +
                "sl_lm_nhanh = EXCLUDED.sl_lm_nhanh, sl_noitinh_nhanh = EXCLUDED.sl_noitinh_nhanh, sl_noimien_nhanh = EXCLUDED.sl_noimien_nhanh, " +
                "sl_lienmien_nhanh = EXCLUDED.sl_lienmien_nhanh, sl_tt_tk = EXCLUDED.sl_tt_tk, sl_mm_tk = EXCLUDED.sl_mm_tk, " +
                "sl_lm_tk = EXCLUDED.sl_lm_tk, sl_noitinh_tk = EXCLUDED.sl_noitinh_tk, sl_noimien_tk = EXCLUDED.sl_noimien_tk, " +
                "sl_lienmien_tk = EXCLUDED.sl_lienmien_tk, sl_tt_kien_tk = EXCLUDED.sl_tt_kien_tk, sl_mm_kien_tk = EXCLUDED.sl_mm_kien_tk, " +
                "sl_lm_kien_tk = EXCLUDED.sl_lm_kien_tk, sl_noitinh_kien_tk = EXCLUDED.sl_noitinh_kien_tk, sl_noimien_kien_tk = EXCLUDED.sl_noimien_kien_tk, " +
                "sl_lienmien_kien_tk = EXCLUDED.sl_lienmien_kien_tk, sl_tt_tach_kien_tk = EXCLUDED.sl_tt_tach_kien_tk, " +
                "sl_mm_tach_kien_tk = EXCLUDED.sl_mm_tach_kien_tk, sl_lm_tach_kien_tk = EXCLUDED.sl_lm_tach_kien_tk, " +
                "sl_noitinh_tach_kien_tk = EXCLUDED.sl_noitinh_tach_kien_tk, sl_noimien_tach_kien_tk = EXCLUDED.sl_noimien_tach_kien_tk, " +
                "sl_lienmien_tach_kien_tk = EXCLUDED.sl_lienmien_tach_kien_tk, sl_fm_all = EXCLUDED.sl_fm_all, " +
                "sl_fm_nhanh = EXCLUDED.sl_fm_nhanh, sl_fm_tk = EXCLUDED.sl_fm_tk, sl_fm_kien_tk = EXCLUDED.sl_fm_kien_tk, " +
                "sl_fm_tach_kien_tk = EXCLUDED.sl_fm_tach_kien_tk," +
                "tg_mm_noimien = EXCLUDED.tg_mm_noimien," +
                "tg_mm_lienmien = EXCLUDED.tg_mm_lienmien";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_INDEX"), config.getConfig("POSTGRES.USER_INDEX"), config.getConfig("POSTGRES.PASSWD_INDEX"))) {
            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(queryTable)) {
                for (LeadTimeAllDto dto : dtoList) {
                    pstmt.setDate(1, Date.valueOf(dto.getNgayBaoCao()));
                    pstmt.setString(2, dto.getMaChiNhanh());
                    pstmt.setString(3, dto.getMaBuuCuc());
                    pstmt.setDouble(4, dto.getTgTtAll());
                    pstmt.setDouble(5, dto.getTgFmAll());
                    pstmt.setDouble(6, dto.getTgMmAll());
                    pstmt.setDouble(7, dto.getTgLmAll());
                    pstmt.setDouble(8, dto.getTgNoiTinhAll());
                    pstmt.setDouble(9, dto.getTgNoiMienAll());
                    pstmt.setDouble(10, dto.getTgLienMienAll());
                    pstmt.setDouble(11, dto.getTgTtNhanh());
                    pstmt.setDouble(12, dto.getTgFmNhanh());
                    pstmt.setDouble(13, dto.getTgMmNhanh());
                    pstmt.setDouble(14, dto.getTgLmNhanh());
                    pstmt.setDouble(15, dto.getTgNoiTinhNhanh());
                    pstmt.setDouble(16, dto.getTgNoiMienNhanh());
                    pstmt.setDouble(17, dto.getTgLienMienNhanh());
                    pstmt.setDouble(18, dto.getTgTtTk());
                    pstmt.setDouble(19, dto.getTgFmTk());
                    pstmt.setDouble(20, dto.getTgMmTk());
                    pstmt.setDouble(21, dto.getTgLmTk());
                    pstmt.setDouble(22, dto.getTgNoiTinhTk());
                    pstmt.setDouble(23, dto.getTgNoiMienTk());
                    pstmt.setDouble(24, dto.getTgLienMienTk());
                    pstmt.setDouble(25, dto.getTgTtKienTk());
                    pstmt.setDouble(26, dto.getTgFmKienTk());
                    pstmt.setDouble(27, dto.getTgMmKienTk());
                    pstmt.setDouble(28, dto.getTgLmKienTk());
                    pstmt.setDouble(29, dto.getTgNoiTinhKienTk());
                    pstmt.setDouble(30, dto.getTgNoiMienKienTk());
                    pstmt.setDouble(31, dto.getTgLienMienKienTk());
                    pstmt.setDouble(32, dto.getTgTtTachKienTk());
                    pstmt.setDouble(33, dto.getTgFmTachKienTk());
                    pstmt.setDouble(34, dto.getTgMmTachKienTk());
                    pstmt.setDouble(35, dto.getTgLmTachKienTk());
                    pstmt.setDouble(36, dto.getTgNoiTinhTachKienTk());
                    pstmt.setDouble(37, dto.getTgNoiMienTachKienTk());
                    pstmt.setDouble(38, dto.getTgLienMienTachKienTk());
                    pstmt.setLong(39, dto.getSlDonAll());
                    pstmt.setLong(40, dto.getSlNoiTinhAll());
                    pstmt.setLong(41, dto.getSlNoiMienAll());
                    pstmt.setLong(42, dto.getSlLienMienAll());
                    pstmt.setLong(43, dto.getSlTtNhanh());
                    pstmt.setLong(44, dto.getSlMmNhanh());
                    pstmt.setLong(45, dto.getSlLmNhanh());
                    pstmt.setLong(46, dto.getSlNoiTinhNhanh());
                    pstmt.setLong(47, dto.getSlNoiMienNhanh());
                    pstmt.setLong(48, dto.getSlLienMienNhanh());
                    pstmt.setLong(49, dto.getSlTtTk());
                    pstmt.setLong(50, dto.getSlMmTk());
                    pstmt.setLong(51, dto.getSlLmTk());
                    pstmt.setLong(52, dto.getSlNoiTinhTk());
                    pstmt.setLong(53, dto.getSlNoiMienTk());
                    pstmt.setLong(54, dto.getSlLienMienTk());
                    pstmt.setLong(55, dto.getSlTtKienTk());
                    pstmt.setLong(56, dto.getSlMmKienTk());
                    pstmt.setLong(57, dto.getSlLmKienTk());
                    pstmt.setLong(58, dto.getSlNoiTinhKienTk());
                    pstmt.setLong(59, dto.getSlNoiMienKienTk());
                    pstmt.setLong(60, dto.getSlLienMienKienTk());
                    pstmt.setLong(61, dto.getSlTtTachKienTk());
                    pstmt.setLong(62, dto.getSlMmTachKienTk());
                    pstmt.setLong(63, dto.getSlLmTachKienTk());
                    pstmt.setLong(64, dto.getSlNoiTinhTachKienTk());
                    pstmt.setLong(65, dto.getSlNoiMienTachKienTk());
                    pstmt.setLong(66, dto.getSlLienMienTachKienTk());
                    pstmt.setLong(67, dto.getSlFmAll());
                    pstmt.setLong(68, dto.getSlFmNhanh());
                    pstmt.setLong(69, dto.getSlFmTk());
                    pstmt.setLong(70, dto.getSlFmKienTk());
                    pstmt.setLong(71, dto.getSlFmTachKienTk());
                    pstmt.setDouble(72, dto.getTgMmNoiMien());
                    pstmt.setDouble(73, dto.getTgMmLienMien());
                    pstmt.addBatch();
                }
                pstmt.executeBatch();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("SQLException" + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException e) {
                    System.out.println("SQLException" + e.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }
    }

    public static void insertLuyKe(Configuration config, List<LeadTimeLkThang> dtoList) {
        String queryTable = "INSERT INTO dash_tct_leadtime_thang (ngay_baocao, ma_chinhanh, ma_buucuc, tg_tt_all, tg_fm_all, tg_mm_all, tg_lm_all, tg_noitinh_all, tg_noimien_all, tg_lienmien_all, tg_tt_nhanh, tg_fm_nhanh, tg_mm_nhanh, tg_lm_nhanh, tg_noitinh_nhanh, tg_noimien_nhanh, tg_lienmien_nhanh, tg_tt_tk, tg_fm_tk, tg_mm_tk, tg_lm_tk, tg_noitinh_tk, tg_noimien_tk, tg_lienmien_tk, tg_tt_kien_tk, tg_fm_kien_tk, tg_mm_kien_tk, tg_lm_kien_tk, tg_noitinh_kien_tk, tg_noimien_kien_tk, tg_lienmien_kien_tk, tg_tt_tach_kien_tk, tg_fm_tach_kien_tk, tg_mm_tach_kien_tk, tg_lm_tach_kien_tk, tg_noitinh_tach_kien_tk, tg_noimien_tach_kien_tk, tg_lienmien_tach_kien_tk,tg_mm_noimien,tg_mm_lienmien) \n" +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n" +
                "ON CONFLICT (ngay_baocao, ma_chinhanh, ma_buucuc) DO UPDATE \n" +
                "SET tg_tt_all = EXCLUDED.tg_tt_all, tg_fm_all = EXCLUDED.tg_fm_all, tg_mm_all = EXCLUDED.tg_mm_all, \n" +
                "tg_lm_all = EXCLUDED.tg_lm_all, tg_noitinh_all = EXCLUDED.tg_noitinh_all, tg_noimien_all = EXCLUDED.tg_noimien_all, \n" +
                "tg_lienmien_all = EXCLUDED.tg_lienmien_all, tg_tt_nhanh = EXCLUDED.tg_tt_nhanh, tg_fm_nhanh = EXCLUDED.tg_fm_nhanh, \n" +
                "tg_mm_nhanh = EXCLUDED.tg_mm_nhanh, tg_lm_nhanh = EXCLUDED.tg_lm_nhanh, tg_noitinh_nhanh = EXCLUDED.tg_noitinh_nhanh, \n" +
                "tg_noimien_nhanh = EXCLUDED.tg_noimien_nhanh, tg_lienmien_nhanh = EXCLUDED.tg_lienmien_nhanh, tg_tt_tk = EXCLUDED.tg_tt_tk, \n" +
                "tg_fm_tk = EXCLUDED.tg_fm_tk, tg_mm_tk = EXCLUDED.tg_mm_tk, tg_lm_tk = EXCLUDED.tg_lm_tk, tg_noitinh_tk = EXCLUDED.tg_noitinh_tk, \n" +
                "tg_noimien_tk = EXCLUDED.tg_noimien_tk, tg_lienmien_tk = EXCLUDED.tg_lienmien_tk, tg_tt_kien_tk = EXCLUDED.tg_tt_kien_tk, \n" +
                "tg_fm_kien_tk = EXCLUDED.tg_fm_kien_tk, tg_mm_kien_tk = EXCLUDED.tg_mm_kien_tk, tg_lm_kien_tk = EXCLUDED.tg_lm_kien_tk, \n" +
                "tg_noitinh_kien_tk = EXCLUDED.tg_noitinh_kien_tk, tg_noimien_kien_tk = EXCLUDED.tg_noimien_kien_tk, \n" +
                "tg_lienmien_kien_tk = EXCLUDED.tg_lienmien_kien_tk, tg_tt_tach_kien_tk = EXCLUDED.tg_tt_tach_kien_tk, \n" +
                "tg_fm_tach_kien_tk = EXCLUDED.tg_fm_tach_kien_tk, tg_mm_tach_kien_tk = EXCLUDED.tg_mm_tach_kien_tk, \n" +
                "tg_lm_tach_kien_tk = EXCLUDED.tg_lm_tach_kien_tk, tg_noitinh_tach_kien_tk = EXCLUDED.tg_noitinh_tach_kien_tk, \n" +
                "tg_noimien_tach_kien_tk = EXCLUDED.tg_noimien_tach_kien_tk, tg_lienmien_tach_kien_tk = EXCLUDED.tg_lienmien_tach_kien_tk,tg_mm_noimien = EXCLUDED.tg_mm_noimien,tg_mm_lienmien = EXCLUDED.tg_mm_lienmien";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_INDEX"), config.getConfig("POSTGRES.USER_INDEX"), config.getConfig("POSTGRES.PASSWD_INDEX"))) {
            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(queryTable)) {
                for (LeadTimeLkThang dto : dtoList) {
                    pstmt.setDate(1, Date.valueOf(dto.getNgayBaoCao()));
                    pstmt.setString(2, dto.getMaChiNhanh());
                    pstmt.setString(3, dto.getMaBuuCuc());
                    pstmt.setDouble(4, dto.getTgTtAll());
                    pstmt.setDouble(5, dto.getTgFmAll());
                    pstmt.setDouble(6, dto.getTgMmAll());
                    pstmt.setDouble(7, dto.getTgLmAll());
                    pstmt.setDouble(8, dto.getTgNoiTinhAll());
                    pstmt.setDouble(9, dto.getTgNoiMienAll());
                    pstmt.setDouble(10, dto.getTgLienMienAll());
                    pstmt.setDouble(11, dto.getTgTtNhanh());
                    pstmt.setDouble(12, dto.getTgFmNhanh());
                    pstmt.setDouble(13, dto.getTgMmNhanh());
                    pstmt.setDouble(14, dto.getTgLmNhanh());
                    pstmt.setDouble(15, dto.getTgNoiTinhNhanh());
                    pstmt.setDouble(16, dto.getTgNoiMienNhanh());
                    pstmt.setDouble(17, dto.getTgLienMienNhanh());
                    pstmt.setDouble(18, dto.getTgTtTk());
                    pstmt.setDouble(19, dto.getTgFmTk());
                    pstmt.setDouble(20, dto.getTgMmTk());
                    pstmt.setDouble(21, dto.getTgLmTk());
                    pstmt.setDouble(22, dto.getTgNoiTinhTk());
                    pstmt.setDouble(23, dto.getTgNoiMienTk());
                    pstmt.setDouble(24, dto.getTgLienMienTk());
                    pstmt.setDouble(25, dto.getTgTtKienTk());
                    pstmt.setDouble(26, dto.getTgFmKienTk());
                    pstmt.setDouble(27, dto.getTgMmKienTk());
                    pstmt.setDouble(28, dto.getTgLmKienTk());
                    pstmt.setDouble(29, dto.getTgNoiTinhKienTk());
                    pstmt.setDouble(30, dto.getTgNoiMienKienTk());
                    pstmt.setDouble(31, dto.getTgLienMienKienTk());
                    pstmt.setDouble(32, dto.getTgTtTachKienTk());
                    pstmt.setDouble(33, dto.getTgFmTachKienTk());
                    pstmt.setDouble(34, dto.getTgMmTachKienTk());
                    pstmt.setDouble(35, dto.getTgLmTachKienTk());
                    pstmt.setDouble(36, dto.getTgNoiTinhTachKienTk());
                    pstmt.setDouble(37, dto.getTgNoiMienTachKienTk());
                    pstmt.setDouble(38, dto.getTgLienMienTachKienTk());
                    pstmt.setDouble(39, dto.getTgMmNoiMien());
                    pstmt.setDouble(40, dto.getTgMmLienMien());
                    pstmt.addBatch();
                }
                pstmt.executeBatch();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("SQLException" + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException e) {
                    System.out.println("SQLException" + e.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }
    }

}
