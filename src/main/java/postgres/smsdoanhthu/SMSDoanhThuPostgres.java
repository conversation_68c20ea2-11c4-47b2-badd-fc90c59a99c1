package postgres.smsdoanhthu;

import configs.Configuration;
import model.smsdoanhthu.SMSDoanhThu;

import java.sql.*;
import java.util.List;

public class SMSDoanhThuPostgres {
    public static void insert(Configuration config, List<SMSDoanhThu> smsDoanhThuList) throws ClassNotFoundException {
        String queryTable = "INSERT INTO sms_doanhthu (ngay_baocao, ma_cn, ma_buucuc, sl_ngay, sl_lk_thang, sl_ck_thang, sl_tt_ck_thang, sl_tt_tbn_thang, " +
                "sl_ck_nam, sl_tt_ck_nam, sl_tt_tbn_nam, " +
                "dt_ngay, dt_lk_thang, dt_kehoach, dt_tlht, tien_do_dt, dt_ck_thang, dt_tt_ck_thang, dt_ck_nam, dt_tt_ck_nam, " +
                "dt_ngay_cp, dt_lk_thang_cp, dt_kehoach_cp, dt_tlht_cp, tien_do_dt_cp, dt_ck_thang_cp, dt_tt_ck_thang_cp, dt_tt_tbn_thang_cp, dt_ck_nam_cp, dt_tt_ck_nam_cp, dt_tt_tbn_nam_cp, " +
                "dt_ngay_vtkho, dt_lk_thang_vtkho, dt_kehoach_vtkho, dt_tlht_vtkho, tien_do_dt_vtkho, dt_ck_thang_vtkho, dt_tt_ck_thang_vtkho, dt_ck_nam_vtkho, dt_tt_ck_nam_vtkho, " +
                "dt_ngay_tmut, dt_lk_thang_tmut, dt_kehoach_tmut, dt_tlht_tmut, tien_do_dt_tmut) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                "ON CONFLICT (ngay_baocao, ma_cn, ma_buucuc) DO UPDATE SET " +
                "sl_ngay = EXCLUDED.sl_ngay, sl_lk_thang = EXCLUDED.sl_lk_thang, sl_ck_thang = EXCLUDED.sl_ck_thang, sl_tt_ck_thang = EXCLUDED.sl_tt_ck_thang, sl_tt_tbn_thang = EXCLUDED.sl_tt_tbn_thang, " +
                "sl_ck_nam = EXCLUDED.sl_ck_nam, sl_tt_ck_nam = EXCLUDED.sl_tt_ck_nam, sl_tt_tbn_nam = EXCLUDED.sl_tt_tbn_nam, " +
                "dt_ngay = EXCLUDED.dt_ngay, dt_lk_thang = EXCLUDED.dt_lk_thang, dt_kehoach = EXCLUDED.dt_kehoach, dt_tlht = EXCLUDED.dt_tlht, tien_do_dt = EXCLUDED.tien_do_dt, dt_ck_thang = EXCLUDED.dt_ck_thang, dt_tt_ck_thang = EXCLUDED.dt_tt_ck_thang, dt_ck_nam = EXCLUDED.dt_ck_nam, dt_tt_ck_nam = EXCLUDED.dt_tt_ck_nam, " +
                "dt_ngay_cp = EXCLUDED.dt_ngay_cp, dt_lk_thang_cp = EXCLUDED.dt_lk_thang_cp, dt_kehoach_cp = EXCLUDED.dt_kehoach_cp, dt_tlht_cp = EXCLUDED.dt_tlht_cp, tien_do_dt_cp = EXCLUDED.tien_do_dt_cp, dt_ck_thang_cp = EXCLUDED.dt_ck_thang_cp, dt_tt_ck_thang_cp = EXCLUDED.dt_tt_ck_thang_cp, dt_tt_tbn_thang_cp = EXCLUDED.dt_tt_tbn_thang_cp, dt_ck_nam_cp = EXCLUDED.dt_ck_nam_cp, dt_tt_ck_nam_cp = EXCLUDED.dt_tt_ck_nam_cp, dt_tt_tbn_nam_cp = EXCLUDED.dt_tt_tbn_nam_cp, " +
                "dt_ngay_vtkho = EXCLUDED.dt_ngay_vtkho, dt_lk_thang_vtkho = EXCLUDED.dt_lk_thang_vtkho, dt_kehoach_vtkho = EXCLUDED.dt_kehoach_vtkho, dt_tlht_vtkho = EXCLUDED.dt_tlht_vtkho, tien_do_dt_vtkho = EXCLUDED.tien_do_dt_vtkho, dt_ck_thang_vtkho = EXCLUDED.dt_ck_thang_vtkho, dt_tt_ck_thang_vtkho = EXCLUDED.dt_tt_ck_thang_vtkho, dt_ck_nam_vtkho = EXCLUDED.dt_ck_nam_vtkho, dt_tt_ck_nam_vtkho = EXCLUDED.dt_tt_ck_nam_vtkho, " +
                "dt_ngay_tmut = EXCLUDED.dt_ngay_tmut, dt_lk_thang_tmut = EXCLUDED.dt_lk_thang_tmut, dt_kehoach_tmut = EXCLUDED.dt_kehoach_tmut, dt_tlht_tmut = EXCLUDED.dt_tlht_tmut, tien_do_dt_tmut = EXCLUDED.tien_do_dt_tmut";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_NOTI"), config.getConfig("POSTGRES.USER_NOTI"), config.getConfig("POSTGRES.PASSWD_NOTI"))) {
            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(queryTable)) {
                for (SMSDoanhThu smsDoanhThu : smsDoanhThuList) {
                    pstmt.setDate(1, Date.valueOf(smsDoanhThu.getNgayBaoCao()));
                    pstmt.setString(2, smsDoanhThu.getMaChiNhanh());
                    pstmt.setString(3, smsDoanhThu.getMaBuuCuc());
                    pstmt.setLong(4, smsDoanhThu.getSlNgay());
                    pstmt.setLong(5, smsDoanhThu.getSlLKThang());
                    pstmt.setLong(6, smsDoanhThu.getSlCKThang());
                    pstmt.setDouble(7, smsDoanhThu.getSlTTCKThang());
                    pstmt.setDouble(8, smsDoanhThu.getSlTTTBNThang());
                    pstmt.setLong(9, smsDoanhThu.getSlCKNam());
                    pstmt.setDouble(10, smsDoanhThu.getSlTTCKNam());
                    pstmt.setDouble(11, smsDoanhThu.getSlTTTBNNam());

                    pstmt.setDouble(12, smsDoanhThu.getDtNgay());
                    pstmt.setDouble(13, smsDoanhThu.getDtLKThang());
                    pstmt.setDouble(14, smsDoanhThu.getDtKeHoach());
                    pstmt.setDouble(15, smsDoanhThu.getTiLeHoanThanh());
                    pstmt.setDouble(16, smsDoanhThu.getTienDoDT());
                    pstmt.setDouble(17, smsDoanhThu.getDtCKThang());
                    pstmt.setDouble(18, smsDoanhThu.getDtTTCKThang());
                    pstmt.setDouble(19, smsDoanhThu.getDtCKNam());
                    pstmt.setDouble(20, smsDoanhThu.getDtTTCKNam());

                    pstmt.setDouble(21, smsDoanhThu.getDtNgayCP() == null ? 0 : smsDoanhThu.getDtNgayCP());
                    pstmt.setDouble(22, smsDoanhThu.getDtLKThangCP() == null ? 0 : smsDoanhThu.getDtLKThangCP());
                    pstmt.setDouble(23, smsDoanhThu.getDtKeHoachCP() == null ? 0 : smsDoanhThu.getDtKeHoachCP());
                    pstmt.setDouble(24, smsDoanhThu.getTiLeHoanThanhKHCP() == null ? 0 : smsDoanhThu.getTiLeHoanThanhKHCP());
                    pstmt.setDouble(25, smsDoanhThu.getTienDoDTCP() == null ? 0 : smsDoanhThu.getTienDoDTCP());
                    pstmt.setDouble(26, smsDoanhThu.getDtCKThangCP() == null ? 0 : smsDoanhThu.getDtCKThangCP());
                    pstmt.setDouble(27, smsDoanhThu.getDtTTCKThangCP() == null ? 0 : smsDoanhThu.getDtTTCKThangCP());
                    pstmt.setDouble(28, smsDoanhThu.getDtTTTBNCKThangCP() == null ? 0 : smsDoanhThu.getDtTTTBNCKThangCP());
                    pstmt.setDouble(29, smsDoanhThu.getDtCKNamCP() == null ? 0 : smsDoanhThu.getDtCKNamCP());
                    pstmt.setDouble(30, smsDoanhThu.getDtTTCKNamCP() == null ? 0 : smsDoanhThu.getDtTTCKNamCP());
                    pstmt.setDouble(31, smsDoanhThu.getDtTTTBNCKNamCP() == null ? 0 : smsDoanhThu.getDtTTTBNCKNamCP());

                    pstmt.setDouble(32, smsDoanhThu.getDtNgayVTKho() == null ? 0 : smsDoanhThu.getDtNgayVTKho());
                    pstmt.setDouble(33, smsDoanhThu.getDtLKThangVTKho() == null ? 0 : smsDoanhThu.getDtLKThangVTKho());
                    pstmt.setDouble(34, smsDoanhThu.getDtKeHoachVTKho() == null ? 0 : smsDoanhThu.getDtKeHoachVTKho());
                    pstmt.setDouble(35, smsDoanhThu.getTlhtKeHoachVTKho() == null ? 0 : smsDoanhThu.getTlhtKeHoachVTKho());
                    pstmt.setDouble(36, smsDoanhThu.getTienDoDTVTKho() == null ? 0 : smsDoanhThu.getTienDoDTVTKho());
                    pstmt.setDouble(37, smsDoanhThu.getDtCKThangVTKho() == null ? 0 : smsDoanhThu.getDtCKThangVTKho());
                    pstmt.setDouble(38, smsDoanhThu.getDtTTCKThangVTKho() == null ? 0 : smsDoanhThu.getDtTTCKThangVTKho());
                    pstmt.setDouble(39, smsDoanhThu.getDtCKNamVTKho() == null ? 0 : smsDoanhThu.getDtCKNamVTKho());
                    pstmt.setDouble(40, smsDoanhThu.getDtTTCKNamVTKho() == null ? 0 : smsDoanhThu.getDtTTCKNamVTKho());

                    pstmt.setDouble(41, smsDoanhThu.getDtNgayTMUT() == null ? 0 : smsDoanhThu.getDtNgayTMUT());
                    pstmt.setDouble(42, smsDoanhThu.getDtLKThangTMUT() == null ? 0 : smsDoanhThu.getDtLKThangTMUT());
                    pstmt.setDouble(43, smsDoanhThu.getDtKeHoachTMUT() == null ? 0 : smsDoanhThu.getDtKeHoachTMUT());
                    pstmt.setDouble(44, smsDoanhThu.getTlhtKeHoachTMUT() == null ? 0 : smsDoanhThu.getTlhtKeHoachTMUT());
                    pstmt.setDouble(45, smsDoanhThu.getTienDoDTTMUT() == null ? 0 : smsDoanhThu.getTienDoDTTMUT());
                    pstmt.addBatch();
                }
                pstmt.executeBatch();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("SQLException" + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException e) {
                    System.out.println("SQLException" + e.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }
    }
}
