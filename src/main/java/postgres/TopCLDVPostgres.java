package postgres;

import configs.Configuration;
import model.cldv_cn.TopModel;

import java.sql.*;
import java.util.List;

public class TopCLDVPostgres {
    public static void insert(Configuration config, List<TopModel> dtoList) {
        String queryTable = "INSERT INTO top_chinhanh_cldv (ngay_baocao, ma_cn, ma_bc,type, is_luyke, tl_thuc_hien, tl_hoanthanh, kpi, tgtt) \n" +
                " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n" +
                " ON CONFLICT (ngay_baocao, ma_cn, ma_bc,type,is_luyke) DO UPDATE \n" +
                " SET " +
                " tl_thuc_hien = EXCLUDED.tl_thuc_hien," +
                " tl_hoanthanh = EXCLUDED.tl_hoanthanh," +
                " kpi = EXCLUDED.kpi," +
                " tgtt = EXCLUDED.tgtt";
        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_INDEX"), config.getConfig("POSTGRES.USER_INDEX"), config.getConfig("POSTGRES.PASSWD_INDEX"))) {
            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(queryTable)) {
                for (TopModel dto : dtoList) {
                    pstmt.setDate(1, Date.valueOf(dto.getNgayBaocao()));
                    pstmt.setString(2, dto.getMaChiNhanh());
                    pstmt.setString(3, dto.getMaBuucuc());
                    pstmt.setString(4, dto.getType());
                    pstmt.setInt(5, dto.getIsLuyKe());
                    pstmt.setDouble(6, dto.getTlThucHien());
                    pstmt.setDouble(7, dto.getTlHoanThanh());
                    pstmt.setDouble(8, dto.getKpi());
                    pstmt.setDouble(9, dto.getTgtt());
                    pstmt.addBatch();
                }
                pstmt.executeBatch();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("SQLException" + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException e) {
                    System.out.println("SQLException" + e.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }
    }

}
