package postgres;

import configs.Configuration;
import model.phanHienDonGachAo.OrderImageDto;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class PhatHienDonGachAoPostgres {
    public static void update(Configuration config, int processStatus, Boolean co_qr, Boolean co_barcode, Boolean co_chuky, String ma_phieugui_dudoan, Float ma_phieugui_tuongdong, String id) throws ClassNotFoundException {
        String update = "update order_image set  process_status = ?, co_qr = ?, co_barcode = ? , co_chuky = ?, ma_phieugui_dudoan = ?, ma_phieugui_tuongdong = ? where id = ?";

//        connect db local
//        Class.forName("org.postgresql.Driver");
//        String conn = "*****************************************";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {
//        try (Connection connection = DriverManager.getConnection(conn, "postgres", "postgres")) {

            connection.setAutoCommit(false);
            try (PreparedStatement pstmt = connection.prepareStatement(update)) {
                pstmt.setInt(1, processStatus);
                pstmt.setBoolean(2, co_qr);
                pstmt.setBoolean(3, co_barcode);
                pstmt.setBoolean(4, co_chuky);
                pstmt.setString(5, ma_phieugui_dudoan);
                pstmt.setFloat(6, ma_phieugui_tuongdong);
                pstmt.setString(7, id);

                pstmt.executeUpdate();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("Error updating row: " + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException rollbackEx) {
                    System.out.println("Rollback failed: " + rollbackEx.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }

    }

    public static void updateProcessStatus(Configuration config, int processStatus, String id) throws ClassNotFoundException {
        String update = " update order_image set process_status = ? where id = ? ";

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(update)) {
                pstmt.setInt(1, processStatus);
                pstmt.setString(2, id);

                pstmt.executeUpdate();
                connection.commit();
            } catch (SQLException ex) {
                System.out.println("Error updating process status: " + ex.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException rollbackEx) {
                    System.out.println("Rollback failed: " + rollbackEx.getMessage());
                }
            }
        } catch (SQLException ex) {
            System.out.println("SQLException" + ex.getMessage());
        }

    }

    public static List<OrderImageDto> getListImgLinkAndId(Configuration config, boolean isOnlyProcessDataImageN1) {
        String query = String.format("select id, img_link, ma_phieugui from order_image where process_status = 0 and  time_created >= %s", config.getConfig("TIME_STAMP_CREATED_IMG"));

        if (isOnlyProcessDataImageN1) {
            query = "select id, img_link, ma_phieugui from order_image " +
                    "where process_status = 0 and time_created >= extract(epoch from (current_date - interval '1 day')) * 1000 " +
                    "and time_created < extract(epoch from current_date) * 1000";
        }
        List<OrderImageDto> listRes = new ArrayList<>();

        try (Connection connection = DriverManager.getConnection(config.getConfig("JDBC.URL_BASE"), config.getConfig("POSTGRES.USER_BASE"), config.getConfig("POSTGRES.PASSWD_BASE"))) {

            connection.setAutoCommit(false);
            try (PreparedStatement preparedStatement = connection.prepareStatement(query)) {
                try (ResultSet resultSet = preparedStatement.executeQuery()) {

                    while (resultSet.next()) {
                        OrderImageDto dto = new OrderImageDto();
                        dto.setId(resultSet.getString("id"));
                        dto.setImgLink(resultSet.getString("img_link"));
                        dto.setMa_phieugui(resultSet.getString("ma_phieugui"));

                        listRes.add(dto);
                    }
                }
            }
        } catch (SQLException e) {
            System.out.println("SQLException" + e.getMessage());
            e.printStackTrace();

        }
        return listRes;
    }
}
